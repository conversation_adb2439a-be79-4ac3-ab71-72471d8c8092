@model BubbleTeaCafe.Models.ViewModels.CartViewModel

@{
    ViewData["Title"] = "Giỏ Hàng";
}

<div class="container py-5">
    <div class="row">
        <div class="col">
            <h1 class="display-5 mb-4">
                <i class="fas fa-shopping-cart me-3"></i>Giỏ Hàng
                @if (Model.TotalItems > 0)
                {
                    <span class="badge bg-primary ms-2">@Model.TotalItems món</span>
                }
            </h1>
        </div>
    </div>

    @if (Model.Items.Any())
    {
        <div class="row">
            <div class="col-lg-8">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">Danh sách sản phẩm</h5>
                    </div>
                    <div class="card-body p-0">
                        @foreach (var item in Model.Items)
                        {
                            <div class="border-bottom p-4">
                                <div class="row align-items-center">
                                    <div class="col-md-2">
                                        <div class="product-image-placeholder bg-light d-flex align-items-center justify-content-center" style="height: 80px; width: 80px; border-radius: 8px;">
                                            @switch (item.Product?.Category?.Name)
                                            {
                                                case "Trà Sữa":
                                                    <i class="fas fa-coffee fa-2x text-primary"></i>
                                                    break;
                                                case "Cà Phê":
                                                    <i class="fas fa-mug-hot fa-2x text-warning"></i>
                                                    break;
                                                case "Trà Trái Cây":
                                                    <i class="fas fa-glass-whiskey fa-2x text-success"></i>
                                                    break;
                                                case "Smoothie":
                                                    <i class="fas fa-blender fa-2x text-info"></i>
                                                    break;
                                                default:
                                                    <i class="fas fa-image fa-2x text-muted"></i>
                                                    break;
                                            }
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <h6 class="mb-1">@item.Product?.Name</h6>
                                        <div class="text-muted small">
                                            @if (!string.IsNullOrEmpty(item.Size))
                                            {
                                                <span class="me-2"><i class="fas fa-expand-arrows-alt me-1"></i>Size @item.Size</span>
                                            }
                                            @if (!string.IsNullOrEmpty(item.Temperature))
                                            {
                                                <span class="me-2">
                                                    <i class="fas fa-thermometer-half me-1"></i>
                                                    @switch (item.Temperature)
                                                    {
                                                        case "Cold": <text>Lạnh</text> break;
                                                        case "Hot": <text>Nóng</text> break;
                                                        case "Iced": <text>Đá</text> break;
                                                        default: <text>@item.Temperature</text> break;
                                                    }
                                                </span>
                                            }
                                            @if (!string.IsNullOrEmpty(item.Sweetness))
                                            {
                                                <span><i class="fas fa-candy-cane me-1"></i>@item.Sweetness</span>
                                            }
                                        </div>
                                        @if (!string.IsNullOrEmpty(item.SpecialInstructions))
                                        {
                                            <div class="text-muted small mt-1">
                                                <i class="fas fa-sticky-note me-1"></i>@item.SpecialInstructions
                                            </div>
                                        }
                                    </div>
                                    <div class="col-md-2">
                                        <div class="price-tag">@item.UnitPrice.ToString("N0")đ</div>
                                    </div>
                                    <div class="col-md-2">
                                        <form method="post" action="@Url.Action("UpdateQuantity", "Cart")" class="d-inline">
                                            @Html.AntiForgeryToken()
                                            <input type="hidden" name="cartItemId" value="@item.CartItemId" />
                                            <div class="input-group input-group-sm">
                                                <button type="button" class="btn btn-outline-secondary" onclick="decreaseQuantity(this)">-</button>
                                                <input type="number" class="form-control text-center" name="quantity" value="@item.Quantity" min="1" max="99" onchange="this.form.submit()">
                                                <button type="button" class="btn btn-outline-secondary" onclick="increaseQuantity(this)">+</button>
                                            </div>
                                        </form>
                                    </div>
                                    <div class="col-md-2 text-end">
                                        <div class="fw-bold mb-2">@item.TotalPrice.ToString("N0")đ</div>
                                        <form method="post" action="@Url.Action("RemoveFromCart", "Cart")" class="d-inline">
                                            @Html.AntiForgeryToken()
                                            <input type="hidden" name="cartItemId" value="@item.CartItemId" />
                                            <button type="submit" class="btn btn-outline-danger btn-sm" onclick="return confirm('Bạn có chắc muốn xóa sản phẩm này?')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                </div>
                
                <div class="mt-3">
                    <form method="post" action="@Url.Action("ClearCart", "Cart")" class="d-inline">
                        @Html.AntiForgeryToken()
                        <button type="submit" class="btn btn-outline-danger" onclick="return confirm('Bạn có chắc muốn xóa toàn bộ giỏ hàng?')">
                            <i class="fas fa-trash me-2"></i>Xóa toàn bộ giỏ hàng
                        </button>
                    </form>
                    <a href="@Url.Action("Menu", "Product")" class="btn btn-outline-primary ms-2">
                        <i class="fas fa-plus me-2"></i>Thêm sản phẩm khác
                    </a>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-calculator me-2"></i>Tổng Đơn Hàng</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Tạm tính:</span>
                            <span>@Model.TotalAmount.ToString("N0")đ</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Phí giao hàng:</span>
                            <span class="text-success">Miễn phí</span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between mb-3">
                            <strong>Tổng cộng:</strong>
                            <strong class="text-primary fs-5">@Model.TotalAmount.ToString("N0")đ</strong>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <a href="#" class="btn btn-primary btn-lg">
                                <i class="fas fa-credit-card me-2"></i>Thanh Toán
                            </a>
                            <a href="@Url.Action("Menu", "Product")" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Tiếp tục mua sắm
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="card border-0 shadow-sm mt-3">
                    <div class="card-body">
                        <h6><i class="fas fa-info-circle me-2"></i>Thông tin giao hàng</h6>
                        <ul class="list-unstyled small text-muted mb-0">
                            <li><i class="fas fa-check text-success me-2"></i>Miễn phí giao hàng cho đơn từ 100.000đ</li>
                            <li><i class="fas fa-check text-success me-2"></i>Thời gian giao hàng: 30-45 phút</li>
                            <li><i class="fas fa-check text-success me-2"></i>Thanh toán khi nhận hàng</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    }
    else
    {
        <div class="text-center py-5">
            <i class="fas fa-shopping-cart fa-4x text-muted mb-4"></i>
            <h3>Giỏ hàng trống</h3>
            <p class="text-muted mb-4">Bạn chưa có sản phẩm nào trong giỏ hàng. Hãy khám phá thực đơn của chúng tôi!</p>
            <a href="@Url.Action("Menu", "Product")" class="btn btn-primary btn-lg">
                <i class="fas fa-utensils me-2"></i>Xem Thực Đơn
            </a>
        </div>
    }
</div>

@section Scripts {
    <script>
        function increaseQuantity(button) {
            const input = button.previousElementSibling;
            const currentValue = parseInt(input.value);
            if (currentValue < 99) {
                input.value = currentValue + 1;
                input.form.submit();
            }
        }

        function decreaseQuantity(button) {
            const input = button.nextElementSibling;
            const currentValue = parseInt(input.value);
            if (currentValue > 1) {
                input.value = currentValue - 1;
                input.form.submit();
            }
        }

        // Update cart badge in navigation
        function updateCartBadge() {
            const badge = document.getElementById('cart-badge');
            if (badge) {
                badge.textContent = '@Model.TotalItems';
            }
        }

        // Call on page load
        updateCartBadge();
    </script>
}
