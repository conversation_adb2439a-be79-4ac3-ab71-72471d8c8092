@{
    ViewData["Title"] = "Quản Lý Ng<PERSON>ời Dùng";
}

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-users me-2 text-primary"></i>Quản Lý Người Dùng
                </h1>
                <div class="d-flex gap-2">
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                        <i class="fas fa-user-plus me-2"></i>Thêm <PERSON>ườ<PERSON> Dù<PERSON>
                    </button>
                    <button class="btn btn-outline-success" onclick="exportUsers()">
                        <i class="fas fa-download me-2"></i>Xuất Excel
                    </button>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">156</h4>
                                    <p class="mb-0">Tổng Người Dùng</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-users fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">142</h4>
                                    <p class="mb-0">Đang Hoạt Động</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-user-check fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">12</h4>
                                    <p class="mb-0">Mới Hôm Nay</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-user-plus fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">14</h4>
                                    <p class="mb-0">Bị Khóa</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-user-lock fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search and Filter -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <input type="text" class="form-control" placeholder="Tìm kiếm theo tên, email..." id="userSearch">
                        </div>
                        <div class="col-md-2">
                            <select class="form-select" id="roleFilter">
                                <option value="">Tất cả vai trò</option>
                                <option value="admin">Admin</option>
                                <option value="staff">Nhân viên</option>
                                <option value="customer">Khách hàng</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <select class="form-select" id="statusFilter">
                                <option value="">Tất cả trạng thái</option>
                                <option value="active">Hoạt động</option>
                                <option value="inactive">Không hoạt động</option>
                                <option value="banned">Bị khóa</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex gap-2">
                                <button class="btn btn-primary" onclick="applyFilters()">
                                    <i class="fas fa-search me-1"></i>Tìm Kiếm
                                </button>
                                <button class="btn btn-outline-secondary" onclick="resetFilters()">
                                    <i class="fas fa-redo me-1"></i>Reset
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Users Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Danh Sách Người Dùng</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>ID</th>
                                    <th>Avatar</th>
                                    <th>Thông Tin</th>
                                    <th>Vai Trò</th>
                                    <th>Trạng Thái</th>
                                    <th>Ngày Tham Gia</th>
                                    <th>Lần Cuối Online</th>
                                    <th>Thao Tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Sample Data -->
                                <tr>
                                    <td>1</td>
                                    <td>
                                        <img src="https://ui-avatars.com/api/?name=Admin&background=007bff&color=fff&size=40" 
                                             alt="Admin" class="rounded-circle" width="40" height="40">
                                    </td>
                                    <td>
                                        <div>
                                            <strong>Admin System</strong>
                                            <br><small class="text-muted"><EMAIL></small>
                                            <br><small class="text-muted">0123456789</small>
                                        </div>
                                    </td>
                                    <td><span class="badge bg-danger">Admin</span></td>
                                    <td><span class="badge bg-success">Hoạt động</span></td>
                                    <td>01/01/2025</td>
                                    <td>
                                        <small class="text-success">
                                            <i class="fas fa-circle me-1"></i>Online
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button class="btn btn-sm btn-outline-info" onclick="viewUser(1)" title="Xem chi tiết">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-primary" onclick="editUser(1)" title="Chỉnh sửa">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td>
                                        <img src="https://ui-avatars.com/api/?name=Nguyen+Van+A&background=28a745&color=fff&size=40" 
                                             alt="Nguyen Van A" class="rounded-circle" width="40" height="40">
                                    </td>
                                    <td>
                                        <div>
                                            <strong>Nguyễn Văn A</strong>
                                            <br><small class="text-muted"><EMAIL></small>
                                            <br><small class="text-muted">0987654321</small>
                                        </div>
                                    </td>
                                    <td><span class="badge bg-info">Khách hàng</span></td>
                                    <td><span class="badge bg-success">Hoạt động</span></td>
                                    <td>10/07/2025</td>
                                    <td>
                                        <small class="text-muted">2 giờ trước</small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button class="btn btn-sm btn-outline-info" onclick="viewUser(2)" title="Xem chi tiết">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-primary" onclick="editUser(2)" title="Chỉnh sửa">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-warning" onclick="banUser(2)" title="Khóa tài khoản">
                                                <i class="fas fa-ban"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>3</td>
                                    <td>
                                        <img src="https://ui-avatars.com/api/?name=Tran+Thi+B&background=ffc107&color=000&size=40" 
                                             alt="Tran Thi B" class="rounded-circle" width="40" height="40">
                                    </td>
                                    <td>
                                        <div>
                                            <strong>Trần Thị B</strong>
                                            <br><small class="text-muted"><EMAIL></small>
                                            <br><small class="text-muted">0369852147</small>
                                        </div>
                                    </td>
                                    <td><span class="badge bg-warning">Nhân viên</span></td>
                                    <td><span class="badge bg-success">Hoạt động</span></td>
                                    <td>05/07/2025</td>
                                    <td>
                                        <small class="text-muted">1 ngày trước</small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button class="btn btn-sm btn-outline-info" onclick="viewUser(3)" title="Xem chi tiết">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-primary" onclick="editUser(3)" title="Chỉnh sửa">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-warning" onclick="banUser(3)" title="Khóa tài khoản">
                                                <i class="fas fa-ban"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <nav aria-label="Page navigation" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <li class="page-item disabled">
                                <a class="page-link" href="#" tabindex="-1">Trước</a>
                            </li>
                            <li class="page-item active">
                                <a class="page-link" href="#">1</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">2</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">3</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">Sau</a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add User Modal -->
<div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Thêm Người Dùng Mới</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addUserForm">
                    <div class="mb-3">
                        <label class="form-label">Họ và tên</label>
                        <input type="text" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Email</label>
                        <input type="email" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Số điện thoại</label>
                        <input type="tel" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Vai trò</label>
                        <select class="form-select" required>
                            <option value="">Chọn vai trò</option>
                            <option value="customer">Khách hàng</option>
                            <option value="staff">Nhân viên</option>
                            <option value="admin">Admin</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Mật khẩu</label>
                        <input type="password" class="form-control" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                <button type="button" class="btn btn-primary" onclick="addUser()">Thêm Người Dùng</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function viewUser(userId) {
            alert('Xem chi tiết người dùng ID: ' + userId);
        }

        function editUser(userId) {
            alert('Chỉnh sửa người dùng ID: ' + userId);
        }

        function banUser(userId) {
            if (confirm('Bạn có chắc chắn muốn khóa tài khoản này?')) {
                alert('Khóa tài khoản người dùng ID: ' + userId);
            }
        }

        function addUser() {
            alert('Thêm người dùng mới');
            document.getElementById('addUserModal').querySelector('.btn-close').click();
        }

        function exportUsers() {
            alert('Xuất danh sách người dùng ra Excel');
        }

        function applyFilters() {
            alert('Áp dụng bộ lọc');
        }

        function resetFilters() {
            document.getElementById('userSearch').value = '';
            document.getElementById('roleFilter').value = '';
            document.getElementById('statusFilter').value = '';
        }
    </script>
}
