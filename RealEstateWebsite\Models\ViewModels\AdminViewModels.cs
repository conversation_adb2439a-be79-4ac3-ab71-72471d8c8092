using System.ComponentModel.DataAnnotations;

namespace RealEstateWebsite.Models.ViewModels
{
    public class AdminDashboardViewModel
    {
        public DashboardStatistics Statistics { get; set; } = new DashboardStatistics();
        public List<Property> RecentProperties { get; set; } = new List<Property>();
        public List<Contact> RecentContacts { get; set; } = new List<Contact>();
        public List<MonthlyStatistic> MonthlyStats { get; set; } = new List<MonthlyStatistic>();
        public List<CategoryStatistic> CategoryStats { get; set; } = new List<CategoryStatistic>();
    }

    public class DashboardStatistics
    {
        public int TotalProperties { get; set; }
        public int PropertiesForSale { get; set; }
        public int PropertiesForRent { get; set; }
        public int SoldProperties { get; set; }
        public int TotalContacts { get; set; }
        public int NewContacts { get; set; }
        public int TotalCategories { get; set; }
        public int TotalUsers { get; set; }
        public decimal TotalValue { get; set; }
        public int ViewsThisMonth { get; set; }
        public int FavoritesThisMonth { get; set; }
    }

    public class MonthlyStatistic
    {
        public string Month { get; set; } = string.Empty;
        public int Properties { get; set; }
        public int Contacts { get; set; }
        public decimal Revenue { get; set; }
    }

    public class CategoryStatistic
    {
        public string CategoryName { get; set; } = string.Empty;
        public int PropertyCount { get; set; }
        public decimal TotalValue { get; set; }
        public string Color { get; set; } = string.Empty;
    }

    public class AdminLoginViewModel
    {
        [Required(ErrorMessage = "Tên đăng nhập là bắt buộc")]
        [StringLength(50, ErrorMessage = "Tên đăng nhập không được vượt quá 50 ký tự")]
        public string Username { get; set; } = string.Empty;

        [Required(ErrorMessage = "Mật khẩu là bắt buộc")]
        [StringLength(100, ErrorMessage = "Mật khẩu không được vượt quá 100 ký tự")]
        public string Password { get; set; } = string.Empty;

        public bool RememberMe { get; set; }
    }

    public class PropertyManagementViewModel
    {
        public List<Property> Properties { get; set; } = new List<Property>();
        public PropertySearchViewModel SearchCriteria { get; set; } = new PropertySearchViewModel();
        public PaginationViewModel Pagination { get; set; } = new PaginationViewModel();
        public List<Category> Categories { get; set; } = new List<Category>();
    }

    public class ContactManagementViewModel
    {
        public List<Contact> Contacts { get; set; } = new List<Contact>();
        public ContactSearchViewModel SearchCriteria { get; set; } = new ContactSearchViewModel();
        public PaginationViewModel Pagination { get; set; } = new PaginationViewModel();
    }

    public class ContactSearchViewModel
    {
        public string? Keyword { get; set; }
        public ContactType? Type { get; set; }
        public ContactStatus? Status { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 20;
    }

    public class UserManagementViewModel
    {
        public List<User> Users { get; set; } = new List<User>();
        public UserSearchViewModel SearchCriteria { get; set; } = new UserSearchViewModel();
        public PaginationViewModel Pagination { get; set; } = new PaginationViewModel();
    }

    public class UserSearchViewModel
    {
        public string? Keyword { get; set; }
        public UserRole? Role { get; set; }
        public bool? IsActive { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 20;
    }

    public class ReportViewModel
    {
        public ReportType Type { get; set; }
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public List<ReportData> Data { get; set; } = new List<ReportData>();
        public ReportSummary Summary { get; set; } = new ReportSummary();
    }

    public class ReportData
    {
        public string Label { get; set; } = string.Empty;
        public decimal Value { get; set; }
        public int Count { get; set; }
        public string Color { get; set; } = string.Empty;
    }

    public class ReportSummary
    {
        public decimal TotalValue { get; set; }
        public int TotalCount { get; set; }
        public decimal AverageValue { get; set; }
        public decimal GrowthRate { get; set; }
    }

    public enum ReportType
    {
        PropertyByCategory = 1,
        PropertyByLocation = 2,
        PropertyByPrice = 3,
        ContactsByType = 4,
        MonthlyTrends = 5,
        UserActivity = 6
    }
}
