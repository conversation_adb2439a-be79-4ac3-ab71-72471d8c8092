using Microsoft.AspNetCore.Mvc;
using BubbleTeaCafe.Models;

namespace BubbleTeaCafe.Controllers
{
    public class AdminController : Controller
    {
        public IActionResult Index()
        {
            // Tạm thời không có authentication, sẽ thêm sau
            return View();
        }

        public IActionResult Products()
        {
            return View();
        }

        public IActionResult Orders()
        {
            return View();
        }

        public IActionResult Users()
        {
            return View();
        }

        public IActionResult Settings()
        {
            return View();
        }

        public IActionResult AddProduct()
        {
            return View();
        }

        [HttpPost]
        public IActionResult AddProduct(Product product)
        {
            if (ModelState.IsValid)
            {
                // Xử lý thêm sản phẩm ở đây
                return RedirectToAction("Products");
            }
            return View(product);
        }

        public IActionResult EditProduct(int id)
        {
            // Tìm sản phẩm theo id
            var product = new Product { ProductId = id, Name = "Sample Product" };
            return View(product);
        }

        [HttpPost]
        public IActionResult EditProduct(Product product)
        {
            if (ModelState.IsValid)
            {
                // Xử lý cập nhật sản phẩm ở đây
                return RedirectToAction("Products");
            }
            return View(product);
        }

        public IActionResult DeleteProduct(int id)
        {
            // Xử lý xóa sản phẩm ở đây
            return RedirectToAction("Products");
        }
    }
}
