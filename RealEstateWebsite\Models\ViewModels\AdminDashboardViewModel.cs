using RealEstateWebsite.Models.Entities;

namespace RealEstateWebsite.Models.ViewModels
{
    public class AdminDashboardViewModel
    {
        public DashboardStatistics Statistics { get; set; } = new DashboardStatistics();
        public List<Property> RecentProperties { get; set; } = new List<Property>();
        public List<Contact> RecentContacts { get; set; } = new List<Contact>();
        public List<User> RecentUsers { get; set; } = new List<User>();
    }

    public class DashboardStatistics
    {
        public int TotalProperties { get; set; }
        public int SoldProperties { get; set; }
        public int TotalUsers { get; set; }
        public int NewContacts { get; set; }
        public int ActiveListings { get; set; }
        public int PendingApprovals { get; set; }
        public decimal TotalRevenue { get; set; }
        public int MonthlyViews { get; set; }
    }
}
