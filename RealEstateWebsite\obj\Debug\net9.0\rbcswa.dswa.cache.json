{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["qBVlLyPyXDtI0d1G/DbWmdYH5mTuItntKm4tfKd0fNg=", "bigJnLpGccFxet8YywEJhDPFdfRsBeCxQqkEPLpLXPk=", "z8BaKO75jfOb6oR4HCEmxsdIoZH8II+BJ98wPQzkBzk=", "FPWpqZBnfQ2wQe3OykwzMpz0pIXYeKnURd0109HrR3Q=", "Cx/4IveMYTzS7Er/JXBmegyTmTyoe/wLaUXy+za22eo=", "txaGt+LxGLeWpWXviuuzpej7uLg7/6LpMx0kVIH2Fgo=", "y96/PzhmUOjAZ1ASf1HW36bduRaW9Cosggm+KxD5fU4=", "3Ym5H2D7ozIvdI/TeNlHy7kvyTCKdKWhj4MwPUBKmpk=", "octnvTpK+ZQ0dLrlKD1ce0mMjfnxHro/cDBHgcMehUc=", "AzEi+3NdBWB9t5qQMqk4owNWXLhb24RK1nu0bScyyck=", "Vtss25Llkj+EWTbrW4hVbQLRSoJLtq/oNGhKtoMMcuw=", "iGBpDVi5Xqy3EopuGNg+XpZCvVwNT9Xw+cT7NyPo8vI=", "zTw2F6VXZQQ9VPG73FX/vo52EcmZmPKNkvnFkJkIImA=", "raFpecatYMgZ4/tDrU3hs+pUV0ShqJDmW0BkvKQEWLQ=", "muw9Q80nyBFnd0U2KLUNHiLJZpOcGTMNeeXmkezaizg=", "nHlzG2Nu1P3/hCPV3vzvSrw3eVGdL9aylAlzii3HOKA=", "5D7XuICBpH9et5wcZaiK37Nx99rq5Gb1PdYdfMyTjEY=", "pVSAhogqsTWXrTZUB3uZtt3jj0Uf5rKW74E52NbEnp4=", "d787e/twS6a+aHa5Psp+Q5Jj4YQFzWQivmrOe3rqjlI=", "KS+h+QzIuRLje+dlFNb9e3X7BzS/jdYLnSSMVcK47UI=", "IxwRGZwg+hssw6U/i/wHR/eEUGBzioTDJ24cT9RTAQ4=", "SQNDwva4EKDDYX0kYkrnynY3Sltsq/imOU1SixkTcgg=", "WUEd7s02hshpPZwdiWZf1OiUic8y18vVma61VpjTHiw=", "27Kkjp2UjKQ8Ne2WIZXaC/7C6pE59XsFu8/qJyPirTk=", "93G3z8qbNEk0ppvuBz4LLWIwkwBULyhh1I8lPDXyl7w=", "pDxJhcbz3sJwcsZ+MT0nn8ZO4lz8gJ/Z2Z1J/alu3/E=", "B26KVkWTXAj6F2aJHMsU1bSUl5Tb/XGrqb1vgNDiJeA=", "9Yyy36ACGsZxcRRkBWSuzxISez23sKx1fisDxQmEgVs=", "wFT9R3VTTvlP9lu2FYRIRdlJpIyuTGSAl4/buWL+JmM=", "jL68Ra2C+lEbpcprLSW4xhNMqgvXvqDw61DTyWdqLrI=", "bLwdZ0QM5nnMXyVkzP6CBROuYK6j7CpzhLbZgrDH7vU=", "WdbGdQuUJiRy88Vcy3BXm0Psl5oEn8glvyWOUitIiUw=", "4jKRI1oggqfuxE6O37eQXx7KRbj9i8hCggXFs9/QZWE=", "SFFmY1k+z/FZDx1SbP+l1ql/cKG5slaMAeLjOfjf+nU=", "40UFgazoG2H1I2HVuU+AHRCNumgD7CPWjsyjCE6aaXE=", "EU/3qWqI9HGvHecS6F+CRrCWND2V1pKcVU5cJ9VHafw=", "7wDzVXY41RqN5fxz7uij0EPQs7hdoVnmM1b8EoNW4AU=", "qnDm9K+jkD0wM4ihGCahom5m2L4qkR+uEgRTz7FPz3E=", "l3c1HwsaJkuslqKd3xoQsQ/M3e4H2CERaAAeFO14PJA=", "+GZGRVue0GHDvjEJF0ACHm5ivO/LH9dDKY9CDtRnX5c=", "SpgOnmRjXByGGBWuAdH+Ix8eVQ1LHuomqZ8i8016eEY=", "gFAhANZBqmpFhDcVfOGfvcSibHswzqO9nh1wbCokNEo=", "OHeAvVvTAM9n00LIg7Twfdo0LQrkS4YPblmwMUpagn8=", "JkebmR01riWmFjq5HxYrERj/GDOC0t/FrmsFLW4dhcU=", "YYbzruN+Tf7EWgM27KBsQw+A7LNSnazWcFCbsYdt26k=", "HGF1+fN/n9O162mdfuO3q4zXLTmXMBlCiLnLW/1wao0=", "oDWNaAqQpOYDuYZWSqXt+7PWkauWaRRWrCKwPjMcSg8=", "xPCrHgm7wVw+5xw6HXtV6aHWpHHPwaTVqqF7v/NctI8=", "NfT8o+8L/e8xL6noxEZBXLEqQzpQaMuXeUyWp/kt/C4=", "miCvdiuY+Ah6uclIfBAZNwtwjqI4BuEaMOSGumEHGAc=", "RYsofvg6+qWgzSvSJ/fmDPqrG97DfpGeYSB7G+Ys/5E=", "BzlLD+TQIhPtfKY2wE/OZGyU8ETJJ6tLWkmM1TtLNec=", "zMG0oxnxM38gTRs8iGrnSidUAYYtVJ1kNbqqv/EnNDQ=", "7BuWlJ2K35kY+C+gAKx7ov5AsWswZEYA29fGH6pWSy4=", "BJKV1OWFBQPQi629qYr1IhS7jul6t3lQYwa89WWiy4o=", "NOZQNyrILHQo9dNDpE09npgEgUCUTNfX7XoKaROuB50=", "BPPmgYVrwUWcdFEc8gps/f6W5B26eFwyIzcEEw1NDNU=", "ZzQYm1fuvE+ZgIto2Dnuo0ayac682BXUSoNVWuS8UCE=", "OKuRL2q6OcbGWWsU+/T7vEQyeSz3NTg9O91FOk81/9A=", "YhYWUCHseERZJDmHkctXgA+Fw7wys6qES38hCKNFNZY=", "OQRRKuyoHaYPkIxq7aucOUZoiJjqKMw7wwdAAwh5w+E=", "JdnMfKHnHPlH7NrvH9nUuBYko/pYny1tnk+kaVAh2EY=", "XVTpQH5bwlvdFGXoBSzVa8avcntzN81rATLTPoPdLh0=", "lHDindi/15bGNYd6MimmxbRzGLao3mA4J7f7o3C/5WU="], "CachedAssets": {"JdnMfKHnHPlH7NrvH9nUuBYko/pYny1tnk+kaVAh2EY=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\1wtxv4xwq1-1bp4dr0p0j.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "css/site#[.{fingerprint=1bp4dr0p0j}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vcd28j225g", "Integrity": "4XsXWfUq4v+Gp4807g3iVgqu8qNNOyKjNNOa6xD9Nns=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\css\\site.css", "FileLength": 1529, "LastWriteTime": "2025-07-17T04:15:43.7761712+00:00"}, "qBVlLyPyXDtI0d1G/DbWmdYH5mTuItntKm4tfKd0fNg=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\h956pvrqw7-61n19gt1b8.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "favicon#[.{fingerprint=61n19gt1b8}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nezsohjtde", "Integrity": "wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\favicon.ico", "FileLength": 2468, "LastWriteTime": "2025-07-17T01:34:00.3424549+00:00"}, "bigJnLpGccFxet8YywEJhDPFdfRsBeCxQqkEPLpLXPk=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\wukesidhvh-xtxxf3hu2r.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "js/site#[.{fingerprint=xtxxf3hu2r}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rl5dcbfpcw", "Integrity": "Ydq7YvQUzhbo68waLZeQvZnPOvmTeQ+HyDqjkA1whsA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\js\\site.js", "FileLength": 189, "LastWriteTime": "2025-07-17T01:34:00.3653374+00:00"}, "z8BaKO75jfOb6oR4HCEmxsdIoZH8II+BJ98wPQzkBzk=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\pcsmx6efmo-bqjiyaj88i.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=bqjiyaj88i}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-07-17T01:34:00.3726674+00:00"}, "FPWpqZBnfQ2wQe3OykwzMpz0pIXYeKnURd0109HrR3Q=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\7q6xe5gi3t-c2jlpeoesf.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-07-17T01:34:00.3469713+00:00"}, "Cx/4IveMYTzS7Er/JXBmegyTmTyoe/wLaUXy+za22eo=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\cpnl0m49qf-erw9l3u2r3.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=erw9l3u2r3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-07-17T01:34:00.3414481+00:00"}, "txaGt+LxGLeWpWXviuuzpej7uLg7/6LpMx0kVIH2Fgo=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\e22e79xpzk-aexeepp0ev.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-07-17T01:34:00.3434547+00:00"}, "y96/PzhmUOjAZ1ASf1HW36bduRaW9Cosggm+KxD5fU4=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\4f2ncrzbb8-d7shbmvgxk.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=d7shbmvgxk}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-07-17T01:34:00.3449609+00:00"}, "3Ym5H2D7ozIvdI/TeNlHy7kvyTCKdKWhj4MwPUBKmpk=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\j0y4fxy0c4-ausgxo2sd3.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-07-17T01:34:00.351309+00:00"}, "octnvTpK+ZQ0dLrlKD1ce0mMjfnxHro/cDBHgcMehUc=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\wi6wb7pv16-k8d9w2qqmf.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=k8d9w2qqmf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-07-17T01:34:00.3479724+00:00"}, "AzEi+3NdBWB9t5qQMqk4owNWXLhb24RK1nu0bScyyck=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\vt8w27x98b-cosvhxvwiu.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-07-17T01:34:00.3424549+00:00"}, "Vtss25Llkj+EWTbrW4hVbQLRSoJLtq/oNGhKtoMMcuw=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\k8yjh8h401-ub07r2b239.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=ub07r2b239}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-07-17T01:34:00.3638291+00:00"}, "iGBpDVi5Xqy3EopuGNg+XpZCvVwNT9Xw+cT7NyPo8vI=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\dkjdbltbe3-fvhpjtyr6v.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-07-17T01:34:00.3479724+00:00"}, "zTw2F6VXZQQ9VPG73FX/vo52EcmZmPKNkvnFkJkIImA=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\mzhpa0bmyn-b7pk76d08c.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=b7pk76d08c}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-07-17T01:34:00.3558221+00:00"}, "raFpecatYMgZ4/tDrU3hs+pUV0ShqJDmW0BkvKQEWLQ=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\eflpavssgm-fsbi9cje9m.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-07-17T01:34:00.351309+00:00"}, "muw9Q80nyBFnd0U2KLUNHiLJZpOcGTMNeeXmkezaizg=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\nk0797iu3j-rzd6atqjts.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rzd6atqjts}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-07-17T01:34:00.3400518+00:00"}, "nHlzG2Nu1P3/hCPV3vzvSrw3eVGdL9aylAlzii3HOKA=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\j9yiv012k3-ee0r1s7dh0.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-07-17T01:34:00.3628293+00:00"}, "5D7XuICBpH9et5wcZaiK37Nx99rq5Gb1PdYdfMyTjEY=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\oeumwl21dh-dxx9fxp4il.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=dxx9fxp4il}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-07-17T01:34:00.3414481+00:00"}, "pVSAhogqsTWXrTZUB3uZtt3jj0Uf5rKW74E52NbEnp4=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\hh817xtub9-jd9uben2k1.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-07-17T01:34:00.3449609+00:00"}, "d787e/twS6a+aHa5Psp+Q5Jj4YQFzWQivmrOe3rqjlI=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\yndnwzu50l-khv3u5hwcm.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=khv3u5hwcm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-07-17T01:34:00.3469713+00:00"}, "KS+h+QzIuRLje+dlFNb9e3X7BzS/jdYLnSSMVcK47UI=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\c0qpzipw63-r4e9w2rdcm.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-07-17T01:34:00.3533042+00:00"}, "IxwRGZwg+hssw6U/i/wHR/eEUGBzioTDJ24cT9RTAQ4=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\51zjvsuk40-lcd1t2u6c8.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=lcd1t2u6c8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-07-17T01:34:00.3449609+00:00"}, "SQNDwva4EKDDYX0kYkrnynY3Sltsq/imOU1SixkTcgg=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\sjke35y3t7-c2oey78nd0.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-07-17T01:34:00.351309+00:00"}, "WUEd7s02hshpPZwdiWZf1OiUic8y18vVma61VpjTHiw=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\a7633ngrbt-tdbxkamptv.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=tdbxkamptv}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-07-17T01:34:00.3578285+00:00"}, "27Kkjp2UjKQ8Ne2WIZXaC/7C6pE59XsFu8/qJyPirTk=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\2c1bxgi0gj-j5mq2jizvt.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-07-17T01:34:00.3618313+00:00"}, "93G3z8qbNEk0ppvuBz4LLWIwkwBULyhh1I8lPDXyl7w=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\40jkkx7fn5-06098lyss8.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=06098lyss8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-07-17T01:34:00.3424549+00:00"}, "pDxJhcbz3sJwcsZ+MT0nn8ZO4lz8gJ/Z2Z1J/alu3/E=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\mo555iwidz-nvvlpmu67g.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-07-17T01:34:00.3459665+00:00"}, "B26KVkWTXAj6F2aJHMsU1bSUl5Tb/XGrqb1vgNDiJeA=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\mlksspz87d-s35ty4nyc5.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=s35ty4nyc5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-07-17T01:34:00.3533042+00:00"}, "9Yyy36ACGsZxcRRkBWSuzxISez23sKx1fisDxQmEgVs=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\f7casuup6v-pj5nd1wqec.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-07-17T01:34:00.3761939+00:00"}, "wFT9R3VTTvlP9lu2FYRIRdlJpIyuTGSAl4/buWL+JmM=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\ecxlc85j8s-46ein0sx1k.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=46ein0sx1k}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-07-17T01:34:00.3792025+00:00"}, "jL68Ra2C+lEbpcprLSW4xhNMqgvXvqDw61DTyWdqLrI=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\coywfe0kgi-v0zj4ognzu.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-07-17T01:34:00.3492928+00:00"}, "bLwdZ0QM5nnMXyVkzP6CBROuYK6j7CpzhLbZgrDH7vU=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\m8yipr0viy-37tfw0ft22.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=37tfw0ft22}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-07-17T01:34:00.3608284+00:00"}, "WdbGdQuUJiRy88Vcy3BXm0Psl5oEn8glvyWOUitIiUw=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\14c4y1vx7m-hrwsygsryq.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-07-17T01:34:00.3822788+00:00"}, "4jKRI1oggqfuxE6O37eQXx7KRbj9i8hCggXFs9/QZWE=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\s13akul9zf-pk9g2wxc8p.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=pk9g2wxc8p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-07-17T01:34:00.3858042+00:00"}, "SFFmY1k+z/FZDx1SbP+l1ql/cKG5slaMAeLjOfjf+nU=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\hs34y1main-ft3s53vfgj.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-07-17T01:34:00.3937771+00:00"}, "40UFgazoG2H1I2HVuU+AHRCNumgD7CPWjsyjCE6aaXE=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\j5p5mq7wuv-6cfz1n2cew.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=6cfz1n2cew}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-07-17T01:34:00.3444554+00:00"}, "EU/3qWqI9HGvHecS6F+CRrCWND2V1pKcVU5cJ9VHafw=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\83cvfy3zkn-6pdc2jztkx.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=6pdc2jztkx}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-07-17T01:34:00.3598276+00:00"}, "7wDzVXY41RqN5fxz7uij0EPQs7hdoVnmM1b8EoNW4AU=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\yq8jnuasv8-493y06b0oq.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=493y06b0oq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-07-17T01:34:00.3736768+00:00"}, "qnDm9K+jkD0wM4ihGCahom5m2L4qkR+uEgRTz7FPz3E=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\s014t65gt8-iovd86k7lj.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=iovd86k7lj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-07-17T01:34:00.3822788+00:00"}, "l3c1HwsaJkuslqKd3xoQsQ/M3e4H2CERaAAeFO14PJA=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\0u5s6cm6rz-vr1egmr9el.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=vr1egmr9el}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-07-17T01:34:00.3868122+00:00"}, "+GZGRVue0GHDvjEJF0ACHm5ivO/LH9dDKY9CDtRnX5c=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\g600h7ytmb-kbrnm935zg.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=kbrnm935zg}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-07-17T01:34:00.3558221+00:00"}, "SpgOnmRjXByGGBWuAdH+Ix8eVQ1LHuomqZ8i8016eEY=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\l35reibh56-jj8uyg4cgr.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=jj8uyg4cgr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-07-17T01:34:00.3588279+00:00"}, "gFAhANZBqmpFhDcVfOGfvcSibHswzqO9nh1wbCokNEo=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\9v5dcuzv85-y7v9cxd14o.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=y7v9cxd14o}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-07-17T01:34:00.3628293+00:00"}, "OHeAvVvTAM9n00LIg7Twfdo0LQrkS4YPblmwMUpagn8=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\izyua02r1o-notf2xhcfb.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=notf2xhcfb}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-07-17T01:34:00.3608284+00:00"}, "JkebmR01riWmFjq5HxYrERj/GDOC0t/FrmsFLW4dhcU=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\rj1k5n2926-h1s4sie4z3.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=h1s4sie4z3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-07-17T01:34:00.3782001+00:00"}, "YYbzruN+Tf7EWgM27KBsQw+A7LNSnazWcFCbsYdt26k=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\l9r8jcxkoh-63fj8s7r0e.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=63fj8s7r0e}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-07-17T01:34:00.3434547+00:00"}, "HGF1+fN/n9O162mdfuO3q4zXLTmXMBlCiLnLW/1wao0=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\uxtwkjhtel-0j3bgjxly4.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=0j3bgjxly4}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-07-17T01:34:00.3608284+00:00"}, "oDWNaAqQpOYDuYZWSqXt+7PWkauWaRRWrCKwPjMcSg8=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\7t05dgoya4-47otxtyo56.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint=47otxtyo56}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mrxpa<PERSON><PERSON><PERSON>", "Integrity": "8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-07-17T01:34:00.3736768+00:00"}, "xPCrHgm7wVw+5xw6HXtV6aHWpHHPwaTVqqF7v/NctI8=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\4qj1soqgrf-4v8eqarkd7.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint=4v8eqarkd7}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw5m9o3hck", "Integrity": "ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-07-17T01:34:00.3568293+00:00"}, "NfT8o+8L/e8xL6noxEZBXLEqQzpQaMuXeUyWp/kt/C4=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\0deb464kwl-356vix0kms.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint=356vix0kms}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-07-17T01:34:00.3568293+00:00"}, "miCvdiuY+Ah6uclIfBAZNwtwjqI4BuEaMOSGumEHGAc=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\ifj8f2hjlv-83jwlth58m.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint=83jwlth58m}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-07-17T01:34:00.3434547+00:00"}, "RYsofvg6+qWgzSvSJ/fmDPqrG97DfpGeYSB7G+Ys/5E=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\vuiakvf2hx-mrlpezrjn3.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint=mrlpezrjn3}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-07-17T01:34:00.3628293+00:00"}, "BzlLD+TQIhPtfKY2wE/OZGyU8ETJJ6tLWkmM1TtLNec=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\ba6phvr5mv-lzl9nlhx6b.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint=lzl9nlhx6b}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anlp8nykfw", "Integrity": "KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-07-17T01:34:00.3882574+00:00"}, "zMG0oxnxM38gTRs8iGrnSidUAYYtVJ1kNbqqv/EnNDQ=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\mq7d3g8z9a-ag7o75518u.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint=ag7o75518u}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgmochx07c", "Integrity": "T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-07-17T01:34:00.3598276+00:00"}, "7BuWlJ2K35kY+C+gAKx7ov5AsWswZEYA29fGH6pWSy4=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\vnel4vs92w-x0q3zqp4vz.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint=x0q3zqp4vz}]?.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-07-17T01:34:00.3608284+00:00"}, "BJKV1OWFBQPQi629qYr1IhS7jul6t3lQYwa89WWiy4o=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\ins2sx3ocj-0i3buxo5is.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint=0i3buxo5is}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "slhp14mdwv", "Integrity": "YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 84431, "LastWriteTime": "2025-07-17T01:34:00.3479724+00:00"}, "NOZQNyrILHQo9dNDpE09npgEgUCUTNfX7XoKaROuB50=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\4u3zzwpede-o1o13a6vjx.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=o1o13a6vjx}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4efnowp72v", "Integrity": "rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-07-17T01:34:00.3558221+00:00"}, "BPPmgYVrwUWcdFEc8gps/f6W5B26eFwyIzcEEw1NDNU=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\tlzzcdb0e2-ttgo8qnofa.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=ttgo8qnofa}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-07-17T01:34:00.3608284+00:00"}, "ZzQYm1fuvE+ZgIto2Dnuo0ayac682BXUSoNVWuS8UCE=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\43ctltkygd-2z0ns9nrw6.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint=2z0ns9nrw6}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlmiz42j7j", "Integrity": "9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-07-17T01:34:00.390269+00:00"}, "OKuRL2q6OcbGWWsU+/T7vEQyeSz3NTg9O91FOk81/9A=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\66mgv3kh3k-muycvpuwrr.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=muycvpuwrr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trtvcfsjjx", "Integrity": "h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-07-17T01:34:00.3927744+00:00"}, "YhYWUCHseERZJDmHkctXgA+Fw7wys6qES38hCKNFNZY=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\tya66cljj5-87fc7y1x7t.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=87fc7y1x7t}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-07-17T01:34:00.3434547+00:00"}, "OQRRKuyoHaYPkIxq7aucOUZoiJjqKMw7wwdAAwh5w+E=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\road19tr06-mlv21k5csn.gz", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint=mlv21k5csn}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-07-17T01:34:00.3444554+00:00"}, "XVTpQH5bwlvdFGXoBSzVa8avcntzN81rATLTPoPdLh0=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\f0i4g8ccsc-yu1flilskb.gz", "SourceId": "RealEstateWebsite", "SourceType": "Computed", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "RealEstateWebsite#[.{fingerprint=yu1flilskb}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\scopedcss\\bundle\\RealEstateWebsite.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "evvb7ovphh", "Integrity": "5pLIWuYWAQTtP8H16Q9sV5t2MMzurXEEDcaSDMGwGkI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\scopedcss\\bundle\\RealEstateWebsite.styles.css", "FileLength": 545, "LastWriteTime": "2025-07-17T01:34:00.3449609+00:00"}, "lHDindi/15bGNYd6MimmxbRzGLao3mA4J7f7o3C/5WU=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\26wshusn8d-yu1flilskb.gz", "SourceId": "RealEstateWebsite", "SourceType": "Computed", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "RealEstateWebsite#[.{fingerprint=yu1flilskb}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\RealEstateWebsite.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "evvb7ovphh", "Integrity": "5pLIWuYWAQTtP8H16Q9sV5t2MMzurXEEDcaSDMGwGkI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\RealEstateWebsite\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\RealEstateWebsite.bundle.scp.css", "FileLength": 545, "LastWriteTime": "2025-07-17T01:34:00.3459665+00:00"}}, "CachedCopyCandidates": {}}