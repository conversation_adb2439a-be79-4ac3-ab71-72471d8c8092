﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Bubble Tea & Coffee Shop</title>
    <script type="importmap"></script>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/BubbleTeaCafe.styles.css" asp-append-version="true" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet" />
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary border-bottom box-shadow mb-3">
            <div class="container">
                <a class="navbar-brand" asp-area="" asp-controller="Home" asp-action="Index">
                    <i class="fas fa-mug-hot me-2"></i>Bubble Tea & Coffee Shop
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target=".navbar-collapse" aria-controls="navbarSupportedContent"
                        aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="navbar-collapse collapse d-lg-inline-flex justify-content-between">
                    <ul class="navbar-nav flex-grow-1">
                        <li class="nav-item">
                            <a class="nav-link text-white" asp-area="" asp-controller="Home" asp-action="Index">
                                <i class="fas fa-home me-1"></i>Trang Chủ
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white" asp-area="" asp-controller="Product" asp-action="Menu">
                                <i class="fas fa-list me-1"></i>Thực Đơn
                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle text-white" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-coffee me-1"></i>Danh Mục
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="navbarDropdown">
                                <li><a class="dropdown-item" asp-controller="Product" asp-action="Category" asp-route-id="1">Trà Sữa</a></li>
                                <li><a class="dropdown-item" asp-controller="Product" asp-action="Category" asp-route-id="2">Cà Phê</a></li>
                                <li><a class="dropdown-item" asp-controller="Product" asp-action="Category" asp-route-id="3">Trà Trái Cây</a></li>
                                <li><a class="dropdown-item" asp-controller="Product" asp-action="Category" asp-route-id="4">Smoothie</a></li>
                            </ul>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white" asp-area="" asp-controller="Home" asp-action="About">
                                <i class="fas fa-info-circle me-1"></i>Giới Thiệu
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white" asp-area="" asp-controller="Home" asp-action="Contact">
                                <i class="fas fa-envelope me-1"></i>Liên Hệ
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white" asp-area="" asp-controller="Home" asp-action="Gallery">
                                <i class="fas fa-images me-1"></i>Thư Viện
                            </a>
                        </li>
                    </ul>
                    <div class="d-flex">
                        <a class="btn btn-outline-light me-2" asp-controller="Cart" asp-action="Index">
                            <i class="fas fa-shopping-cart"></i>
                            <span id="cart-badge" class="badge bg-danger ms-1">0</span>
                        </a>
                    </div>
                </div>
            </div>
        </nav>
    </header>
    <div class="container">
        <main role="main" class="pb-3">
            @RenderBody()
        </main>
    </div>

    <footer class="border-top footer text-muted bg-light mt-5">
        <div class="container py-4">
            <div class="row">
                <div class="col-md-4">
                    <h5><i class="fas fa-mug-hot me-2"></i>Bubble Tea & Coffee Shop</h5>
                    <p class="text-muted">Thưởng thức những ly trà sữa và cà phê tuyệt vời nhất tại cửa hàng của chúng tôi.</p>
                </div>
                <div class="col-md-4">
                    <h6>Liên Hệ</h6>
                    <p class="text-muted mb-1"><i class="fas fa-map-marker-alt me-2"></i>123 Đường ABC, Quận 1, TP.HCM</p>
                    <p class="text-muted mb-1"><i class="fas fa-phone me-2"></i>0123 456 789</p>
                    <p class="text-muted mb-1"><i class="fas fa-envelope me-2"></i><EMAIL></p>
                </div>
                <div class="col-md-4">
                    <h6>Giờ Mở Cửa</h6>
                    <p class="text-muted mb-1">Thứ 2 - Thứ 6: 7:00 - 22:00</p>
                    <p class="text-muted mb-1">Thứ 7 - Chủ Nhật: 8:00 - 23:00</p>
                </div>
            </div>
            <hr>
            <div class="text-center">
                &copy; 2025 - Bubble Tea & Coffee Shop. All rights reserved.
            </div>
        </div>
    </footer>
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
