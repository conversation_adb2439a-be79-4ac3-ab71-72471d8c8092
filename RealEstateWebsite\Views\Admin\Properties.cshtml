@model RealEstateWebsite.Models.ViewModels.PropertiesListViewModel

@{
    ViewData["Title"] = "Quản Lý Bất Động Sản";
    Layout = "~/Views/Shared/_AdminLayout.cshtml";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0 text-gray-800">Quản Lý Bất Động Sản</h1>
        <p class="mb-0 text-muted">Quản lý tất cả bất động sản trên hệ thống</p>
    </div>
    <div>
        <a asp-action="CreateProperty" class="btn btn-success">
            <i class="fas fa-plus"></i> Thêm BDS Mới
        </a>
    </div>
</div>

<!-- Search and Filter -->
<div class="card shadow mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label class="form-label">T<PERSON><PERSON> kiếm</label>
                <input type="text" name="search" value="@Model.SearchTerm" class="form-control" placeholder="Nhập tiêu đề, địa chỉ...">
            </div>
            <div class="col-md-3">
                <label class="form-label">Danh mục</label>
                <select name="category" class="form-select">
                    <option value="">Tất cả danh mục</option>
                    @foreach (var category in ViewBag.Categories as List<RealEstateWebsite.Models.Entities.Category>)
                    {
                        <option value="@category.Name" selected="@(Model.SelectedCategory == category.Name)">@category.Name</option>
                    }
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Trạng thái</label>
                <select name="status" class="form-select">
                    <option value="">Tất cả trạng thái</option>
                    <option value="Đang bán" selected="@(Model.SelectedStatus == "Đang bán")">Đang bán</option>
                    <option value="Đã bán" selected="@(Model.SelectedStatus == "Đã bán")">Đã bán</option>
                    <option value="Đang thuê" selected="@(Model.SelectedStatus == "Đang thuê")">Đang thuê</option>
                    <option value="Đã thuê" selected="@(Model.SelectedStatus == "Đã thuê")">Đã thuê</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <button type="submit" class="btn btn-primary d-block w-100">
                    <i class="fas fa-search"></i> Tìm kiếm
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Properties Table -->
<div class="card shadow">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Danh Sách Bất Động Sản</h6>
    </div>
    <div class="card-body">
        @if (Model.Properties.Any())
        {
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Hình ảnh</th>
                            <th>Tiêu đề</th>
                            <th>Danh mục</th>
                            <th>Giá</th>
                            <th>Địa chỉ</th>
                            <th>Trạng thái</th>
                            <th>Ngày tạo</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var property in Model.Properties)
                        {
                            <tr>
                                <td>
                                    <img src="@(!string.IsNullOrEmpty(property.MainImage) ? property.MainImage : "https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80")" 
                                         class="rounded" style="width: 60px; height: 60px; object-fit: cover;" alt="@property.Title">
                                </td>
                                <td>
                                    <strong>@property.Title</strong><br>
                                    <small class="text-muted">@property.Bedrooms PN • @property.Bathrooms PT • @property.Area m²</small>
                                </td>
                                <td>
                                    <span class="badge bg-info">@property.Category?.Name</span>
                                </td>
                                <td>
                                    <strong class="text-danger">@property.Price.ToString("N0") VNĐ</strong>
                                    @if (property.Type == 2)
                                    {
                                        <small class="text-muted">/tháng</small>
                                    }
                                </td>
                                <td>
                                    <small>@property.Address<br>@property.City</small>
                                </td>
                                <td>
                                    @if (property.Status == "Đang bán" || property.Status == "Đang thuê")
                                    {
                                        <span class="badge bg-success">@property.Status</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-secondary">@property.Status</span>
                                    }
                                </td>
                                <td>
                                    <small>@property.CreatedDate.ToString("dd/MM/yyyy")</small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a asp-controller="Properties" asp-action="Details" asp-route-id="@property.PropertyId" 
                                           class="btn btn-sm btn-outline-primary" title="Xem chi tiết">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a asp-action="EditProperty" asp-route-id="@property.PropertyId" 
                                           class="btn btn-sm btn-outline-warning" title="Chỉnh sửa">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                onclick="deleteProperty(@property.PropertyId)" title="Xóa">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if (Model.TotalPages > 1)
            {
                <nav aria-label="Page navigation" class="mt-4">
                    <ul class="pagination justify-content-center">
                        @if (Model.CurrentPage > 1)
                        {
                            <li class="page-item">
                                <a class="page-link" href="?page=@(Model.CurrentPage - 1)&search=@Model.SearchTerm&category=@Model.SelectedCategory&status=@Model.SelectedStatus">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                        }

                        @for (int i = Math.Max(1, Model.CurrentPage - 2); i <= Math.Min(Model.TotalPages, Model.CurrentPage + 2); i++)
                        {
                            <li class="page-item @(i == Model.CurrentPage ? "active" : "")">
                                <a class="page-link" href="?page=@i&search=@Model.SearchTerm&category=@Model.SelectedCategory&status=@Model.SelectedStatus">@i</a>
                            </li>
                        }

                        @if (Model.CurrentPage < Model.TotalPages)
                        {
                            <li class="page-item">
                                <a class="page-link" href="?page=@(Model.CurrentPage + 1)&search=@Model.SearchTerm&category=@Model.SelectedCategory&status=@Model.SelectedStatus">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        }
                    </ul>
                </nav>
            }
        }
        else
        {
            <div class="text-center py-5">
                <i class="fas fa-home fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Không tìm thấy bất động sản nào</h5>
                <p class="text-muted">Hãy thử thay đổi bộ lọc hoặc thêm bất động sản mới.</p>
                <a asp-action="CreateProperty" class="btn btn-success">
                    <i class="fas fa-plus"></i> Thêm BDS Mới
                </a>
            </div>
        }
    </div>
</div>

@section Scripts {
    <script>
        function deleteProperty(propertyId) {
            if (confirm('Bạn có chắc chắn muốn xóa bất động sản này?')) {
                fetch('/Admin/DeleteProperty', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                    },
                    body: JSON.stringify({ id: propertyId })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Có lỗi xảy ra: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Có lỗi xảy ra khi xóa bất động sản');
                });
            }
        }
    </script>
}
