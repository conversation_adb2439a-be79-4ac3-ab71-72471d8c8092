@model RealEstateWebsite.Models.ViewModels.UsersListViewModel

@{
    ViewData["Title"] = "Quản Lý Người Dùng";
    Layout = "~/Views/Shared/_AdminLayout.cshtml";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0 text-gray-800">Quản Lý Người Dùng</h1>
        <p class="mb-0 text-muted">Quản lý tất cả người dùng trên hệ thống</p>
    </div>
    <div>
        <a asp-action="CreateUser" class="btn btn-success">
            <i class="fas fa-plus"></i> Thêm Người Dùng
        </a>
    </div>
</div>

<!-- Search -->
<div class="card shadow mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-8">
                <label class="form-label">T<PERSON><PERSON> kiếm</label>
                <input type="text" name="search" value="@Model.SearchTerm" class="form-control" placeholder="Nhập tên, email...">
            </div>
            <div class="col-md-4">
                <label class="form-label">&nbsp;</label>
                <button type="submit" class="btn btn-primary d-block w-100">
                    <i class="fas fa-search"></i> Tìm kiếm
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Users Table -->
<div class="card shadow">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Danh Sách Người Dùng</h6>
    </div>
    <div class="card-body">
        @if (Model.Users.Any())
        {
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Avatar</th>
                            <th>Thông tin</th>
                            <th>Vai trò</th>
                            <th>Trạng thái</th>
                            <th>Ngày tạo</th>
                            <th>Lần đăng nhập cuối</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var user in Model.Users)
                        {
                            <tr>
                                <td>
                                    <img src="@(!string.IsNullOrEmpty(user.Avatar) ? user.Avatar : "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=60&q=80")" 
                                         class="rounded-circle" style="width: 50px; height: 50px; object-fit: cover;" alt="@user.FullName">
                                </td>
                                <td>
                                    <strong>@user.FullName</strong><br>
                                    <small class="text-muted">@user.Email</small><br>
                                    @if (!string.IsNullOrEmpty(user.Phone))
                                    {
                                        <small class="text-muted">📞 @user.Phone</small>
                                    }
                                </td>
                                <td>
                                    @if (user.Role == "Admin")
                                    {
                                        <span class="badge bg-danger">Admin</span>
                                    }
                                    else if (user.Role == "Agent")
                                    {
                                        <span class="badge bg-warning">Môi giới</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-info">Khách hàng</span>
                                    }
                                </td>
                                <td>
                                    @if (user.IsActive)
                                    {
                                        <span class="badge bg-success">Hoạt động</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-secondary">Tạm khóa</span>
                                    }
                                </td>
                                <td>
                                    <small>@user.CreatedDate.ToString("dd/MM/yyyy")</small>
                                </td>
                                <td>
                                    @if (user.LastLoginDate.HasValue)
                                    {
                                        <small>@user.LastLoginDate.Value.ToString("dd/MM/yyyy HH:mm")</small>
                                    }
                                    else
                                    {
                                        <small class="text-muted">Chưa đăng nhập</small>
                                    }
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a asp-action="UserDetails" asp-route-id="@user.UserId" 
                                           class="btn btn-sm btn-outline-primary" title="Xem chi tiết">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a asp-action="EditUser" asp-route-id="@user.UserId" 
                                           class="btn btn-sm btn-outline-warning" title="Chỉnh sửa">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        @if (user.IsActive)
                                        {
                                            <button type="button" class="btn btn-sm btn-outline-secondary" 
                                                    onclick="toggleUserStatus(@user.UserId, false)" title="Khóa tài khoản">
                                                <i class="fas fa-lock"></i>
                                            </button>
                                        }
                                        else
                                        {
                                            <button type="button" class="btn btn-sm btn-outline-success" 
                                                    onclick="toggleUserStatus(@user.UserId, true)" title="Mở khóa tài khoản">
                                                <i class="fas fa-unlock"></i>
                                            </button>
                                        }
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if (Model.TotalPages > 1)
            {
                <nav aria-label="Page navigation" class="mt-4">
                    <ul class="pagination justify-content-center">
                        @if (Model.CurrentPage > 1)
                        {
                            <li class="page-item">
                                <a class="page-link" href="?page=@(Model.CurrentPage - 1)&search=@Model.SearchTerm">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                        }

                        @for (int i = Math.Max(1, Model.CurrentPage - 2); i <= Math.Min(Model.TotalPages, Model.CurrentPage + 2); i++)
                        {
                            <li class="page-item @(i == Model.CurrentPage ? "active" : "")">
                                <a class="page-link" href="?page=@i&search=@Model.SearchTerm">@i</a>
                            </li>
                        }

                        @if (Model.CurrentPage < Model.TotalPages)
                        {
                            <li class="page-item">
                                <a class="page-link" href="?page=@(Model.CurrentPage + 1)&search=@Model.SearchTerm">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        }
                    </ul>
                </nav>
            }
        }
        else
        {
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Không tìm thấy người dùng nào</h5>
                <p class="text-muted">Hãy thử thay đổi từ khóa tìm kiếm hoặc thêm người dùng mới.</p>
                <a asp-action="CreateUser" class="btn btn-success">
                    <i class="fas fa-plus"></i> Thêm Người Dùng
                </a>
            </div>
        }
    </div>
</div>

@section Scripts {
    <script>
        function toggleUserStatus(userId, isActive) {
            const action = isActive ? 'mở khóa' : 'khóa';
            if (confirm(`Bạn có chắc chắn muốn ${action} tài khoản này?`)) {
                fetch('/Admin/ToggleUserStatus', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                    },
                    body: JSON.stringify({ userId: userId, isActive: isActive })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Có lỗi xảy ra: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Có lỗi xảy ra khi thay đổi trạng thái tài khoản');
                });
            }
        }
    </script>
}
