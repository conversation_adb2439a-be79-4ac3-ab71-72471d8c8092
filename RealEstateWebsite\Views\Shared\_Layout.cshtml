﻿<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Bất Động Sản Việt Nam</title>

    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />

    @await RenderSectionAsync("Styles", required: false)
</head>
<body>
    <!-- Header -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" asp-controller="Home" asp-action="Index">
                🏠 Bất Động Sản VN
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="Home" asp-action="Index">
                            <i class="fas fa-home"></i> Trang Chủ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="Properties" asp-action="Index">
                            <i class="fas fa-search"></i> Tìm Kiếm BDS
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="Home" asp-action="About">
                            <i class="fas fa-info-circle"></i> Giới Thiệu
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="Home" asp-action="Contact">
                            <i class="fas fa-phone"></i> Liên Hệ
                        </a>
                    </li>
                </ul>

                <div class="navbar-nav">
                    <a class="nav-link" asp-controller="Admin" asp-action="Login">
                        <i class="fas fa-user-shield"></i> Quản Trị
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        @RenderBody()
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>🏠 Bất Động Sản Việt Nam</h5>
                    <p>Website bất động sản hàng đầu Việt Nam</p>
                </div>
                <div class="col-md-6 text-end">
                    <p>&copy; 2024 Bất Động Sản VN. All rights reserved.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
