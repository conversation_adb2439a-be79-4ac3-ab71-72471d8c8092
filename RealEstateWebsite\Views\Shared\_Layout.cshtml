﻿<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Bất Động Sản Việt Nam</title>

    <!-- SEO Meta Tags -->
    <meta name="description" content="@ViewData["MetaDescription"] ?? "Website bất động sản hàng đầu Việt Nam - <PERSON>án, cho thuê nhà đất, chung cư, biệt thự"" />
    <meta name="keywords" content="@ViewData["MetaKeywords"] ?? "bất động sản, nh<PERSON> đất, chung cư, bi<PERSON><PERSON> thự, mua bán nhà, cho thuê nhà"" />

    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />

    @await RenderSectionAsync("Styles", required: false)
</head>
<body>
    <!-- Header -->
    <header class="main-header">
        <!-- Top Bar -->
        <div class="top-bar">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="contact-info">
                            <span><i class="fas fa-phone"></i> Hotline: 1900-1234</span>
                            <span><i class="fas fa-envelope"></i> <EMAIL></span>
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <div class="social-links">
                            <a href="#" class="social-link"><i class="fab fa-facebook-f"></i></a>
                            <a href="#" class="social-link"><i class="fab fa-youtube"></i></a>
                            <a href="#" class="social-link"><i class="fab fa-zalo"></i></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Navigation -->
        <nav class="navbar navbar-expand-lg main-nav">
            <div class="container">
                <a class="navbar-brand" asp-controller="Home" asp-action="Index">
                    <span class="brand-text">🏠 Bất Động Sản VN</span>
                </a>

                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Home" asp-action="Index">
                                <i class="fas fa-home"></i> Trang Chủ
                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-building"></i> Bất Động Sản
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" asp-controller="Properties" asp-action="Index" asp-route-type="Sale">Nhà Đất Bán</a></li>
                                <li><a class="dropdown-item" asp-controller="Properties" asp-action="Index" asp-route-type="Rent">Nhà Đất Cho Thuê</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" asp-controller="Properties" asp-action="Index" asp-route-categoryId="1">Nhà Phố</a></li>
                                <li><a class="dropdown-item" asp-controller="Properties" asp-action="Index" asp-route-categoryId="2">Chung Cư</a></li>
                                <li><a class="dropdown-item" asp-controller="Properties" asp-action="Index" asp-route-categoryId="3">Biệt Thự</a></li>
                                <li><a class="dropdown-item" asp-controller="Properties" asp-action="Index" asp-route-categoryId="4">Đất Nền</a></li>
                            </ul>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Properties" asp-action="Index">
                                <i class="fas fa-search"></i> Tìm Kiếm
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Home" asp-action="About">
                                <i class="fas fa-info-circle"></i> Giới Thiệu
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Home" asp-action="Contact">
                                <i class="fas fa-phone"></i> Liên Hệ
                            </a>
                        </li>
                    </ul>

                    <div class="navbar-actions">
                        <a class="btn btn-outline-primary me-2" asp-controller="Properties" asp-action="Favorites">
                            <i class="fas fa-heart"></i> Yêu Thích
                        </a>
                        <a class="btn btn-primary" href="#">
                            <i class="fas fa-plus"></i> Đăng Tin
                        </a>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main>
        @RenderBody()
    </main>

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
