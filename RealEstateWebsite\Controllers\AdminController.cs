using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using RealEstateWebsite.Data;
using RealEstateWebsite.Models;
using RealEstateWebsite.Models.ViewModels;
using RealEstateWebsite.Models.Entities;

namespace RealEstateWebsite.Controllers;

public class AdminController : Controller
{
    private readonly RealEstateContext _context;

    public AdminController(RealEstateContext context)
    {
        _context = context;
    }

    // GET: Admin/Login
    public IActionResult Login()
    {
        // Check if already logged in
        if (HttpContext.Session.GetString("AdminLoggedIn") == "true")
        {
            return RedirectToAction("Index");
        }
        
        return View();
    }

    // POST: Admin/Login
    [HttpPost]
    public async Task<IActionResult> Login(AdminLoginViewModel model)
    {
        if (ModelState.IsValid)
        {
            var user = await _context.Users
                .FirstOrDefaultAsync(u => u.Username == model.Username && u.IsActive);

            if (user != null && BCrypt.Net.BCrypt.Verify(model.Password, user.PasswordHash))
            {
                if (user.Role == UserRole.Admin || user.Role == UserRole.SuperAdmin)
                {
                    HttpContext.Session.SetString("AdminLoggedIn", "true");
                    HttpContext.Session.SetString("AdminUsername", user.Username);
                    HttpContext.Session.SetString("AdminRole", user.Role.ToString());
                    
                    // Update last login
                    user.LastLoginDate = DateTime.Now;
                    await _context.SaveChangesAsync();
                    
                    return RedirectToAction("Index");
                }
                else
                {
                    ViewData["ErrorMessage"] = "Bạn không có quyền truy cập vào trang quản trị!";
                }
            }
            else
            {
                ViewData["ErrorMessage"] = "Tên đăng nhập hoặc mật khẩu không đúng!";
            }
        }
        
        return View(model);
    }

    // GET: Admin/Logout
    public IActionResult Logout()
    {
        HttpContext.Session.Clear();
        return RedirectToAction("Login");
    }

    // Check authentication for all admin actions
    private bool IsAuthenticated()
    {
        return HttpContext.Session.GetString("AdminLoggedIn") == "true";
    }

    public async Task<IActionResult> Index()
    {
        if (!IsAuthenticated())
            return RedirectToAction("Login");

        var viewModel = new AdminDashboardViewModel();

        // Get statistics
        viewModel.Statistics.TotalProperties = await _context.Properties.CountAsync();
        viewModel.Statistics.SoldProperties = await _context.Properties.CountAsync(p => p.Status == PropertyStatus.Sold);
        viewModel.Statistics.TotalUsers = await _context.Users.CountAsync();
        viewModel.Statistics.NewContacts = await _context.Contacts.CountAsync(c => c.CreatedDate >= DateTime.Now.AddDays(-7));

        // Get recent properties
        viewModel.RecentProperties = await _context.Properties
            .Include(p => p.Category)
            .OrderByDescending(p => p.CreatedDate)
            .Take(5)
            .ToListAsync();

        // Get recent contacts
        viewModel.RecentContacts = await _context.Contacts
            .OrderByDescending(c => c.CreatedDate)
            .Take(5)
            .ToListAsync();

        return View(viewModel);
    }

    public async Task<IActionResult> Properties(PropertySearchViewModel searchModel)
    {
        if (!IsAuthenticated())
            return RedirectToAction("Login");

        var query = _context.Properties.Include(p => p.Category).AsQueryable();

        // Apply search filters (similar to PropertiesController)
        if (!string.IsNullOrEmpty(searchModel.Keyword))
        {
            query = query.Where(p => p.Title.Contains(searchModel.Keyword) ||
                                   p.Description.Contains(searchModel.Keyword));
        }

        if (searchModel.CategoryId.HasValue)
        {
            query = query.Where(p => p.CategoryId == searchModel.CategoryId.Value);
        }

        // Apply sorting
        query = searchModel.SortBy switch
        {
            PropertySortBy.Price => searchModel.SortDirection == SortDirection.Ascending 
                ? query.OrderBy(p => p.Price) 
                : query.OrderByDescending(p => p.Price),
            PropertySortBy.Area => searchModel.SortDirection == SortDirection.Ascending 
                ? query.OrderBy(p => p.Area) 
                : query.OrderByDescending(p => p.Area),
            PropertySortBy.Title => searchModel.SortDirection == SortDirection.Ascending 
                ? query.OrderBy(p => p.Title) 
                : query.OrderByDescending(p => p.Title),
            _ => query.OrderByDescending(p => p.CreatedDate)
        };

        var totalItems = await query.CountAsync();
        var properties = await query
            .Skip((searchModel.Page - 1) * searchModel.PageSize)
            .Take(searchModel.PageSize)
            .ToListAsync();

        var categories = await _context.Categories
            .Where(c => c.IsActive)
            .OrderBy(c => c.DisplayOrder)
            .ToListAsync();

        var viewModel = new PropertyManagementViewModel
        {
            Properties = properties,
            SearchCriteria = searchModel,
            Pagination = new PaginationViewModel
            {
                CurrentPage = searchModel.Page,
                TotalPages = (int)Math.Ceiling((double)totalItems / searchModel.PageSize),
                TotalItems = totalItems,
                PageSize = searchModel.PageSize
            },
            Categories = categories
        };

        return View(viewModel);
    }

    public async Task<IActionResult> Contacts(ContactSearchViewModel searchModel)
    {
        if (!IsAuthenticated())
            return RedirectToAction("Login");

        var query = _context.Contacts.Include(c => c.Property).AsQueryable();

        // Apply search filters
        if (!string.IsNullOrEmpty(searchModel.Keyword))
        {
            query = query.Where(c => c.FullName.Contains(searchModel.Keyword) ||
                                   c.Email.Contains(searchModel.Keyword) ||
                                   c.Phone.Contains(searchModel.Keyword) ||
                                   c.Message.Contains(searchModel.Keyword));
        }

        if (searchModel.Type.HasValue)
        {
            query = query.Where(c => c.Type == searchModel.Type.Value);
        }

        if (searchModel.Status.HasValue)
        {
            query = query.Where(c => c.Status == searchModel.Status.Value);
        }

        if (searchModel.FromDate.HasValue)
        {
            query = query.Where(c => c.CreatedDate >= searchModel.FromDate.Value);
        }

        if (searchModel.ToDate.HasValue)
        {
            query = query.Where(c => c.CreatedDate <= searchModel.ToDate.Value);
        }

        query = query.OrderByDescending(c => c.CreatedDate);

        var totalItems = await query.CountAsync();
        var contacts = await query
            .Skip((searchModel.Page - 1) * searchModel.PageSize)
            .Take(searchModel.PageSize)
            .ToListAsync();

        var viewModel = new ContactManagementViewModel
        {
            Contacts = contacts,
            SearchCriteria = searchModel,
            Pagination = new PaginationViewModel
            {
                CurrentPage = searchModel.Page,
                TotalPages = (int)Math.Ceiling((double)totalItems / searchModel.PageSize),
                TotalItems = totalItems,
                PageSize = searchModel.PageSize
            }
        };

        return View(viewModel);
    }

    public async Task<IActionResult> Users(UserSearchViewModel searchModel)
    {
        if (!IsAuthenticated())
            return RedirectToAction("Login");

        var query = _context.Users.AsQueryable();

        // Apply search filters
        if (!string.IsNullOrEmpty(searchModel.Keyword))
        {
            query = query.Where(u => u.Username.Contains(searchModel.Keyword) ||
                                   u.Email.Contains(searchModel.Keyword) ||
                                   u.FullName.Contains(searchModel.Keyword));
        }

        if (searchModel.Role.HasValue)
        {
            query = query.Where(u => u.Role == searchModel.Role.Value);
        }

        if (searchModel.IsActive.HasValue)
        {
            query = query.Where(u => u.IsActive == searchModel.IsActive.Value);
        }

        query = query.OrderByDescending(u => u.CreatedDate);

        var totalItems = await query.CountAsync();
        var users = await query
            .Skip((searchModel.Page - 1) * searchModel.PageSize)
            .Take(searchModel.PageSize)
            .ToListAsync();

        var viewModel = new UserManagementViewModel
        {
            Users = users,
            SearchCriteria = searchModel,
            Pagination = new PaginationViewModel
            {
                CurrentPage = searchModel.Page,
                TotalPages = (int)Math.Ceiling((double)totalItems / searchModel.PageSize),
                TotalItems = totalItems,
                PageSize = searchModel.PageSize
            }
        };

        return View(viewModel);
    }

    public IActionResult Categories()
    {
        if (!IsAuthenticated())
            return RedirectToAction("Login");
            
        return View();
    }

    public IActionResult Reports()
    {
        if (!IsAuthenticated())
            return RedirectToAction("Login");
            
        return View();
    }

    private string GetCategoryColor(int categoryId)
    {
        var colors = new[] { "#007bff", "#28a745", "#ffc107", "#dc3545", "#6f42c1", "#fd7e14" };
        return colors[(categoryId - 1) % colors.Length];
    }
}
