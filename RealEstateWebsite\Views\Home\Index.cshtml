﻿@model RealEstateWebsite.Models.ViewModels.HomeViewModel

@{
    ViewData["Title"] = "Trang Chủ";
    ViewData["MetaDescription"] = "Website bất động sản hàng đầu Việt Nam - Tìm kiếm nhà đất, chung cư, bi<PERSON><PERSON> thự";
    ViewData["MetaKeywords"] = "bất động sản, nh<PERSON> đất, chung cư, bi<PERSON><PERSON> thự, mua bán nhà";
}

<!-- Hero Section with Beautiful Images -->
<section class="hero-section">
    <div class="hero-carousel">
        <!-- Slide 1 -->
        <div class="hero-slide active" style="background-image: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)), url('https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');">
            <div class="container">
                <div class="row align-items-center min-vh-100">
                    <div class="col-lg-8">
                        <div class="hero-content text-white">
                            <h1 class="hero-title">
                                Tìm Kiếm <span class="text-warning">Bất Động Sản</span><br>
                                Hoàn Hảo Cho Bạn
                            </h1>
                            <p class="hero-subtitle">
                                Khám phá hàng nghìn bất động sản chất lượng cao với giá tốt nhất.
                                Chúng tôi giúp bạn tìm được ngôi nhà mơ ước.
                            </p>
                            <div class="hero-stats">
                                <div class="stat-item">
                                    <h3>@Model.Statistics.TotalProperties+</h3>
                                    <p>Bất Động Sản</p>
                                </div>
                                <div class="stat-item">
                                    <h3>@Model.Statistics.HappyCustomers+</h3>
                                    <p>Khách Hàng Hài Lòng</p>
                                </div>
                                <div class="stat-item">
                                    <h3>@Model.Statistics.YearsExperience+</h3>
                                    <p>Năm Kinh Nghiệm</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 2 -->
        <div class="hero-slide" style="background-image: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)), url('https://images.unsplash.com/photo-1582407947304-fd86f028f716?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');">
            <div class="container">
                <div class="row align-items-center min-vh-100">
                    <div class="col-lg-8">
                        <div class="hero-content text-white">
                            <h1 class="hero-title">
                                <span class="text-warning">Chung Cư Cao Cấp</span><br>
                                Tiện Nghi Hiện Đại
                            </h1>
                            <p class="hero-subtitle">
                                Sống trong không gian hiện đại với đầy đủ tiện ích.
                                Trải nghiệm cuộc sống đẳng cấp tại các dự án chung cư cao cấp.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 3 -->
        <div class="hero-slide" style="background-image: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)), url('https://images.unsplash.com/photo-1564013799919-ab600027ffc6?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');">
            <div class="container">
                <div class="row align-items-center min-vh-100">
                    <div class="col-lg-8">
                        <div class="hero-content text-white">
                            <h1 class="hero-title">
                                <span class="text-warning">Biệt Thự Sang Trọng</span><br>
                                Không Gian Riêng Tư
                            </h1>
                            <p class="hero-subtitle">
                                Tận hưởng cuộc sống thượng lưu trong những căn biệt thự đẳng cấp.
                                Không gian rộng rãi, thiết kế tinh tế và vị trí đắc địa.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation Arrows -->
        <button class="carousel-nav prev" onclick="changeSlide(-1)">
            <i class="fas fa-chevron-left"></i>
        </button>
        <button class="carousel-nav next" onclick="changeSlide(1)">
            <i class="fas fa-chevron-right"></i>
        </button>

        <!-- Dots Indicator -->
        <div class="carousel-dots">
            <span class="dot active" onclick="currentSlide(1)"></span>
            <span class="dot" onclick="currentSlide(2)"></span>
            <span class="dot" onclick="currentSlide(3)"></span>
        </div>
    </div>

    <!-- Search Form Overlay -->
    <div class="search-overlay">
        <div class="container">
            <div class="row justify-content-end">
                <div class="col-lg-5">
                    <div class="hero-search-form">
                        <div class="search-form-header">
                            <h3>Tìm Kiếm Bất Động Sản</h3>
                            <p>Nhập thông tin để tìm kiếm bất động sản phù hợp</p>
                        </div>

                        <form asp-controller="Properties" asp-action="Index" method="get" class="search-form">
                            <div class="form-tabs">
                                <input type="radio" name="Type" value="1" id="sale-tab" checked>
                                <label for="sale-tab">Mua Bán</label>

                                <input type="radio" name="Type" value="2" id="rent-tab">
                                <label for="rent-tab">Cho Thuê</label>
                            </div>

                            <div class="form-group">
                                <label>Từ khóa</label>
                                <input type="text" name="Keyword" class="form-control" placeholder="Nhập địa chỉ, tên dự án...">
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Loại bất động sản</label>
                                        <select name="CategoryId" class="form-select">
                                            <option value="">Tất cả</option>
                                            @foreach (var category in Model.Categories)
                                            {
                                                <option value="@category.CategoryId">@category.Name</option>
                                            }
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Thành phố</label>
                                        <select name="City" class="form-select">
                                            <option value="">Tất cả</option>
                                            <option value="Hồ Chí Minh">TP. Hồ Chí Minh</option>
                                            <option value="Hà Nội">Hà Nội</option>
                                            <option value="Đà Nẵng">Đà Nẵng</option>
                                            <option value="Cần Thơ">Cần Thơ</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Giá từ (VNĐ)</label>
                                        <select name="MinPrice" class="form-select">
                                            <option value="">Không giới hạn</option>
                                            <option value="500000000">500 triệu</option>
                                            <option value="1000000000">1 tỷ</option>
                                            <option value="2000000000">2 tỷ</option>
                                            <option value="5000000000">5 tỷ</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Giá đến (VNĐ)</label>
                                        <select name="MaxPrice" class="form-select">
                                            <option value="">Không giới hạn</option>
                                            <option value="1000000000">1 tỷ</option>
                                            <option value="2000000000">2 tỷ</option>
                                            <option value="5000000000">5 tỷ</option>
                                            <option value="10000000000">10 tỷ</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary btn-search">
                                <i class="fas fa-search"></i> Tìm Kiếm
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Properties Section -->
<section class="section-padding">
    <div class="container">
        <div class="section-header text-center">
            <h2 class="section-title">Bất Động Sản Nổi Bật</h2>
            <p class="section-subtitle">Khám phá những bất động sản chất lượng cao được nhiều người quan tâm</p>
        </div>

        <div class="row">
            @if (Model.FeaturedProperties != null && Model.FeaturedProperties.Any())
            {
                @foreach (var property in Model.FeaturedProperties.Take(6))
                {
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="property-card">
                            <div class="property-image">
                                <img src="@(!string.IsNullOrEmpty(property.MainImage) ? property.MainImage : "https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80")"
                                     alt="@property.Title" class="img-fluid">
                                <div class="property-badges">
                                    <span class="badge bg-danger">VIP</span>
                                    @if (property.Type == 1)
                                    {
                                        <span class="badge bg-success">Bán</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-info">Thuê</span>
                                    }
                                </div>
                                <div class="property-overlay">
                                    <a asp-controller="Properties" asp-action="Details" asp-route-id="@property.PropertyId"
                                       class="btn btn-light btn-sm">
                                        <i class="fas fa-eye"></i> Xem Chi Tiết
                                    </a>
                                </div>
                            </div>
                            <div class="property-content">
                                <div class="property-price">
                                    @property.Price.ToString("N0") VNĐ
                                    @if (property.Type == 2)
                                    {
                                        <span class="price-unit">/tháng</span>
                                    }
                                </div>
                                <h5 class="property-title">
                                    <a asp-controller="Properties" asp-action="Details" asp-route-id="@property.PropertyId">
                                        @property.Title
                                    </a>
                                </h5>
                                <p class="property-location">
                                    <i class="fas fa-map-marker-alt"></i>
                                    @property.Address, @property.City
                                </p>
                                <div class="property-features">
                                    <span><i class="fas fa-bed"></i> @property.Bedrooms phòng ngủ</span>
                                    <span><i class="fas fa-bath"></i> @property.Bathrooms phòng tắm</span>
                                    <span><i class="fas fa-ruler-combined"></i> @property.Area m²</span>
                                </div>
                                <div class="property-footer">
                                    <div class="property-agent">
                                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=40&q=80"
                                             alt="Agent" class="agent-avatar">
                                        <span>Môi giới</span>
                                    </div>
                                    <div class="property-date">
                                        @property.CreatedDate.ToString("dd/MM/yyyy")
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                }
            }
            else
            {
                <!-- Demo Properties -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="property-card">
                        <div class="property-image">
                            <img src="https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80"
                                 alt="Nhà phố hiện đại" class="img-fluid">
                            <div class="property-badges">
                                <span class="badge bg-danger">VIP</span>
                                <span class="badge bg-success">Bán</span>
                            </div>
                            <div class="property-overlay">
                                <a href="#" class="btn btn-light btn-sm">
                                    <i class="fas fa-eye"></i> Xem Chi Tiết
                                </a>
                            </div>
                        </div>
                        <div class="property-content">
                            <div class="property-price">
                                10.500.000.000 VNĐ
                            </div>
                            <h5 class="property-title">
                                <a href="#">Nhà Phố Hiện Đại Quận 7</a>
                            </h5>
                            <p class="property-location">
                                <i class="fas fa-map-marker-alt"></i>
                                Đường Nguyễn Thị Thập, Quận 7, TP.HCM
                            </p>
                            <div class="property-features">
                                <span><i class="fas fa-bed"></i> 4 phòng ngủ</span>
                                <span><i class="fas fa-bath"></i> 3 phòng tắm</span>
                                <span><i class="fas fa-ruler-combined"></i> 120 m²</span>
                            </div>
                            <div class="property-footer">
                                <div class="property-agent">
                                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=40&q=80"
                                         alt="Agent" class="agent-avatar">
                                    <span>Nguyễn Văn A</span>
                                </div>
                                <div class="property-date">
                                    15/12/2024
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="property-card">
                        <div class="property-image">
                            <img src="https://images.unsplash.com/photo-1582407947304-fd86f028f716?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80"
                                 alt="Chung cư cao cấp" class="img-fluid">
                            <div class="property-badges">
                                <span class="badge bg-warning">Hot</span>
                                <span class="badge bg-info">Thuê</span>
                            </div>
                            <div class="property-overlay">
                                <a href="#" class="btn btn-light btn-sm">
                                    <i class="fas fa-eye"></i> Xem Chi Tiết
                                </a>
                            </div>
                        </div>
                        <div class="property-content">
                            <div class="property-price">
                                25.000.000 VNĐ <span class="price-unit">/tháng</span>
                            </div>
                            <h5 class="property-title">
                                <a href="#">Chung Cư Vinhomes Central Park</a>
                            </h5>
                            <p class="property-location">
                                <i class="fas fa-map-marker-alt"></i>
                                Đường Nguyễn Hữu Cảnh, Bình Thạnh, TP.HCM
                            </p>
                            <div class="property-features">
                                <span><i class="fas fa-bed"></i> 2 phòng ngủ</span>
                                <span><i class="fas fa-bath"></i> 2 phòng tắm</span>
                                <span><i class="fas fa-ruler-combined"></i> 85 m²</span>
                            </div>
                            <div class="property-footer">
                                <div class="property-agent">
                                    <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=40&q=80"
                                         alt="Agent" class="agent-avatar">
                                    <span>Trần Thị B</span>
                                </div>
                                <div class="property-date">
                                    14/12/2024
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="property-card">
                        <div class="property-image">
                            <img src="https://images.unsplash.com/photo-1564013799919-ab600027ffc6?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80"
                                 alt="Biệt thự sang trọng" class="img-fluid">
                            <div class="property-badges">
                                <span class="badge bg-danger">VIP</span>
                                <span class="badge bg-success">Bán</span>
                            </div>
                            <div class="property-overlay">
                                <a href="#" class="btn btn-light btn-sm">
                                    <i class="fas fa-eye"></i> Xem Chi Tiết
                                </a>
                            </div>
                        </div>
                        <div class="property-content">
                            <div class="property-price">
                                ************** VNĐ
                            </div>
                            <h5 class="property-title">
                                <a href="#">Biệt Thự Phú Mỹ Hưng</a>
                            </h5>
                            <p class="property-location">
                                <i class="fas fa-map-marker-alt"></i>
                                Khu đô thị Phú Mỹ Hưng, Quận 7, TP.HCM
                            </p>
                            <div class="property-features">
                                <span><i class="fas fa-bed"></i> 5 phòng ngủ</span>
                                <span><i class="fas fa-bath"></i> 4 phòng tắm</span>
                                <span><i class="fas fa-ruler-combined"></i> 300 m²</span>
                            </div>
                            <div class="property-footer">
                                <div class="property-agent">
                                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=40&q=80"
                                         alt="Agent" class="agent-avatar">
                                    <span>Lê Văn C</span>
                                </div>
                                <div class="property-date">
                                    13/12/2024
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>

        <div class="text-center mt-4">
            <a asp-controller="Properties" asp-action="Index" class="btn btn-primary btn-lg">
                Xem Tất Cả Bất Động Sản <i class="fas fa-arrow-right ms-2"></i>
            </a>
        </div>
    </div>
</section>

<!-- Categories Section -->
<section class="section-padding bg-light">
    <div class="container">
        <div class="section-header text-center">
            <h2 class="section-title">Danh Mục Bất Động Sản</h2>
            <p class="section-subtitle">Khám phá các loại bất động sản đa dạng phù hợp với nhu cầu của bạn</p>
        </div>

        <div class="row">
            @foreach (var category in Model.Categories)
            {
                <div class="col-lg-2 col-md-4 col-6 mb-4">
                    <a asp-controller="Properties" asp-action="Index" asp-route-categoryId="@category.CategoryId" class="category-card">
                        <div class="category-icon">
                            <i class="@category.Icon"></i>
                        </div>
                        <h5 class="category-name">@category.Name</h5>
                        <p class="category-count">@category.Properties.Count bất động sản</p>
                    </a>
                </div>
            }
        </div>
    </div>
</section>

@section Scripts {
    <script>
        // Hero Carousel
        let currentSlide = 0;
        const slides = document.querySelectorAll('.hero-slide');
        const dots = document.querySelectorAll('.dot');
        const totalSlides = slides.length;

        function showSlide(index) {
            // Remove active class from all slides and dots
            slides.forEach(slide => slide.classList.remove('active'));
            dots.forEach(dot => dot.classList.remove('active'));

            // Add active class to current slide and dot
            slides[index].classList.add('active');
            dots[index].classList.add('active');
        }

        function nextSlide() {
            currentSlide = (currentSlide + 1) % totalSlides;
            showSlide(currentSlide);
        }

        function prevSlide() {
            currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;
            showSlide(currentSlide);
        }

        function changeSlide(direction) {
            if (direction === 1) {
                nextSlide();
            } else {
                prevSlide();
            }
        }

        function currentSlideFunc(index) {
            currentSlide = index - 1;
            showSlide(currentSlide);
        }

        // Auto-play carousel
        setInterval(nextSlide, 5000);

        // Search form enhancements
        document.addEventListener('DOMContentLoaded', function() {
            // Form tabs functionality
            const tabs = document.querySelectorAll('input[name="Type"]');
            tabs.forEach(tab => {
                tab.addEventListener('change', function() {
                    // You can add different styling or behavior for sale vs rent
                    console.log('Selected type:', this.value);
                });
            });

            // Add smooth scrolling to property cards
            const propertyCards = document.querySelectorAll('.property-card');
            propertyCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
}
