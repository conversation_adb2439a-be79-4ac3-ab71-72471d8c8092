﻿@model RealEstateWebsite.Models.ViewModels.HomeViewModel

@{
    ViewData["Title"] = "Trang Chủ";
    ViewData["MetaDescription"] = "Website bất động sản hàng đầu Việt Nam - Tìm kiếm nhà đất, chung cư, bi<PERSON>t thự";
    ViewData["MetaKeywords"] = "bất động sản, nh<PERSON> đất, chung cư, biệt thự, mua bán nhà";
}

<!-- Hero Section -->
<section class="hero-section">
    <div class="hero-background">
        <div class="container">
            <div class="row align-items-center min-vh-100">
                <div class="col-lg-6">
                    <div class="hero-content">
                        <h1 class="hero-title">
                            Tìm <PERSON> <span class="text-primary">Bất Động Sản</span><br>
                            Hoàn Hảo Cho Bạn
                        </h1>
                        <p class="hero-subtitle">
                            Khám phá hàng nghìn bất động sản chất lượng cao với giá tốt nhất.
                            Chúng tôi giúp bạn tìm được ngôi nhà mơ ước.
                        </p>
                        <div class="hero-stats">
                            <div class="stat-item">
                                <h3>@Model.Statistics.TotalProperties+</h3>
                                <p>Bất Động Sản</p>
                            </div>
                            <div class="stat-item">
                                <h3>@Model.Statistics.HappyCustomers+</h3>
                                <p>Khách Hàng Hài Lòng</p>
                            </div>
                            <div class="stat-item">
                                <h3>@Model.Statistics.YearsExperience+</h3>
                                <p>Năm Kinh Nghiệm</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="hero-search-form">
                        <div class="search-form-header">
                            <h3>Tìm Kiếm Bất Động Sản</h3>
                            <p>Nhập thông tin để tìm kiếm bất động sản phù hợp</p>
                        </div>

                        <form asp-controller="Properties" asp-action="Index" method="get" class="search-form">
                            <div class="form-tabs">
                                <input type="radio" name="Type" value="1" id="sale-tab" checked>
                                <label for="sale-tab">Mua Bán</label>

                                <input type="radio" name="Type" value="2" id="rent-tab">
                                <label for="rent-tab">Cho Thuê</label>
                            </div>

                            <div class="form-group">
                                <label>Từ khóa</label>
                                <input type="text" name="Keyword" class="form-control" placeholder="Nhập địa chỉ, tên dự án...">
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Loại bất động sản</label>
                                        <select name="CategoryId" class="form-select">
                                            <option value="">Tất cả</option>
                                            @foreach (var category in Model.Categories)
                                            {
                                                <option value="@category.CategoryId">@category.Name</option>
                                            }
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Thành phố</label>
                                        <select name="City" class="form-select">
                                            <option value="">Tất cả</option>
                                            <option value="Hồ Chí Minh">TP. Hồ Chí Minh</option>
                                            <option value="Hà Nội">Hà Nội</option>
                                            <option value="Đà Nẵng">Đà Nẵng</option>
                                            <option value="Cần Thơ">Cần Thơ</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Giá từ (VNĐ)</label>
                                        <select name="MinPrice" class="form-select">
                                            <option value="">Không giới hạn</option>
                                            <option value="500000000">500 triệu</option>
                                            <option value="1000000000">1 tỷ</option>
                                            <option value="2000000000">2 tỷ</option>
                                            <option value="5000000000">5 tỷ</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Giá đến (VNĐ)</label>
                                        <select name="MaxPrice" class="form-select">
                                            <option value="">Không giới hạn</option>
                                            <option value="1000000000">1 tỷ</option>
                                            <option value="2000000000">2 tỷ</option>
                                            <option value="5000000000">5 tỷ</option>
                                            <option value="10000000000">10 tỷ</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary btn-search">
                                <i class="fas fa-search"></i> Tìm Kiếm
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Categories Section -->
<section class="section-padding bg-light">
    <div class="container">
        <div class="section-header text-center">
            <h2 class="section-title">Danh Mục Bất Động Sản</h2>
            <p class="section-subtitle">Khám phá các loại bất động sản đa dạng phù hợp với nhu cầu của bạn</p>
        </div>

        <div class="row">
            @foreach (var category in Model.Categories)
            {
                <div class="col-lg-2 col-md-4 col-6 mb-4">
                    <a asp-controller="Properties" asp-action="Index" asp-route-categoryId="@category.CategoryId" class="category-card">
                        <div class="category-icon">
                            <i class="@category.Icon"></i>
                        </div>
                        <h5 class="category-name">@category.Name</h5>
                        <p class="category-count">@category.Properties.Count bất động sản</p>
                    </a>
                </div>
            }
        </div>
    </div>
</section>
