/* ===== GLOBAL STYLES ===== */
:root {
    --primary-color: #2563eb;
    --secondary-color: #64748b;
    --success-color: #059669;
    --warning-color: #d97706;
    --danger-color: #dc2626;
    --dark-color: #1e293b;
    --light-color: #f8fafc;
    --border-color: #e2e8f0;
    --text-color: #334155;
    --text-muted: #64748b;
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* ===== HERO CAROUSEL STYLES ===== */
.hero-section {
    position: relative;
    height: 100vh;
    overflow: hidden;
}

.hero-carousel {
    position: relative;
    height: 100%;
}

.hero-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 0;
    transition: opacity 1s ease-in-out;
    display: flex;
    align-items: center;
}

.hero-slide.active {
    opacity: 1;
}

.hero-content {
    z-index: 2;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.2;
    margin-bottom: 1.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.hero-stats {
    display: flex;
    gap: 2rem;
    margin-top: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-item h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #fbbf24;
    margin-bottom: 0.5rem;
}

.stat-item p {
    font-size: 0.9rem;
    opacity: 0.8;
}

.carousel-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255,255,255,0.2);
    border: none;
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 3;
}

.carousel-nav:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-50%) scale(1.1);
}

.carousel-nav.prev {
    left: 30px;
}

.carousel-nav.next {
    right: 30px;
}

.carousel-dots {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
    z-index: 3;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255,255,255,0.5);
    cursor: pointer;
    transition: all 0.3s ease;
}

.dot.active,
.dot:hover {
    background: white;
    transform: scale(1.2);
}

.search-overlay {
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    display: flex;
    align-items: center;
    z-index: 2;
    padding: 2rem 0;
}

/* ===== PROPERTY CARD STYLES ===== */
.property-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
}

.property-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.property-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.property-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.property-card:hover .property-image img {
    transform: scale(1.05);
}

.property-badges {
    position: absolute;
    top: 15px;
    left: 15px;
    z-index: 2;
}

.property-badges .badge {
    margin-right: 5px;
    font-size: 0.75rem;
    padding: 5px 10px;
}

.property-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.property-card:hover .property-overlay {
    opacity: 1;
}

.property-content {
    padding: 1.5rem;
}

.property-price {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--danger-color);
    margin-bottom: 0.5rem;
}

.price-unit {
    font-size: 0.9rem;
    color: var(--text-muted);
    font-weight: 400;
}

.property-title {
    margin-bottom: 0.75rem;
}

.property-title a {
    color: var(--dark-color);
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.property-title a:hover {
    color: var(--primary-color);
}

.property-location {
    color: var(--text-muted);
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.property-location i {
    margin-right: 5px;
    color: var(--danger-color);
}

.property-features {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    font-size: 0.85rem;
    color: var(--text-muted);
}

.property-features span {
    display: flex;
    align-items: center;
    gap: 5px;
}

.property-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.property-agent {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.85rem;
    color: var(--text-muted);
}

.agent-avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    object-fit: cover;
}

.property-date {
    font-size: 0.8rem;
    color: var(--text-muted);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: #ffffff;
}

/* ===== HEADER STYLES ===== */
.main-header {
    position: sticky;
    top: 0;
    z-index: 1000;
    background: white;
    box-shadow: var(--shadow);
}

.top-bar {
    background: var(--dark-color);
    color: white;
    padding: 8px 0;
    font-size: 14px;
}

.top-bar .contact-info span {
    margin-right: 20px;
}

.top-bar .contact-info i {
    margin-right: 5px;
    color: var(--primary-color);
}

.social-links {
    display: flex;
    gap: 10px;
}

.social-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: var(--success-color);
    color: white;
    transform: translateY(-2px);
}

.main-nav {
    padding: 15px 0;
    background: white;
}

.navbar-brand {
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-color) !important;
    text-decoration: none;
}

.brand-text {
    background: linear-gradient(135deg, var(--primary-color), var(--success-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.navbar-nav .nav-link {
    font-weight: 500;
    color: var(--text-color) !important;
    padding: 10px 15px !important;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
    background: var(--light-color);
}

.navbar-nav .nav-link i {
    margin-right: 5px;
}

.dropdown-menu {
    border: none;
    box-shadow: var(--shadow-lg);
    border-radius: 12px;
    padding: 10px 0;
    margin-top: 10px;
}

.dropdown-item {
    padding: 10px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background: var(--light-color);
    color: var(--primary-color);
}

.navbar-actions .btn {
    border-radius: 25px;
    padding: 8px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--success-color));
    border: none;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-outline-primary {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

/* ===== UTILITY CLASSES ===== */
.section-padding {
    padding: 80px 0;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 1rem;
    text-align: center;
}

.section-subtitle {
    font-size: 1.1rem;
    color: var(--text-muted);
    text-align: center;
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.card {
    border: none;
    border-radius: 16px;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.card-img-top {
    height: 250px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.card:hover .card-img-top {
    transform: scale(1.05);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .top-bar {
        text-align: center;
    }

    .top-bar .contact-info {
        margin-bottom: 10px;
    }

    .navbar-actions {
        margin-top: 15px;
    }

    .section-title {
        font-size: 2rem;
    }

    .section-padding {
        padding: 50px 0;
    }
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

.form-floating > .form-control-plaintext::placeholder, .form-floating > .form-control::placeholder {
  color: var(--bs-secondary-color);
  text-align: end;
}

.form-floating > .form-control-plaintext:focus::placeholder, .form-floating > .form-control:focus::placeholder {
  text-align: start;
}