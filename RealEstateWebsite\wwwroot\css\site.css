/* ===== GLOBAL STYLES ===== */
:root {
    --primary-color: #2563eb;
    --secondary-color: #64748b;
    --success-color: #059669;
    --warning-color: #d97706;
    --danger-color: #dc2626;
    --dark-color: #1e293b;
    --light-color: #f8fafc;
    --border-color: #e2e8f0;
    --text-color: #334155;
    --text-muted: #64748b;
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: #ffffff;
}

/* ===== HEADER STYLES ===== */
.main-header {
    position: sticky;
    top: 0;
    z-index: 1000;
    background: white;
    box-shadow: var(--shadow);
}

.top-bar {
    background: var(--dark-color);
    color: white;
    padding: 8px 0;
    font-size: 14px;
}

.top-bar .contact-info span {
    margin-right: 20px;
}

.top-bar .contact-info i {
    margin-right: 5px;
    color: var(--primary-color);
}

.social-links {
    display: flex;
    gap: 10px;
}

.social-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: var(--success-color);
    color: white;
    transform: translateY(-2px);
}

.main-nav {
    padding: 15px 0;
    background: white;
}

.navbar-brand {
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-color) !important;
    text-decoration: none;
}

.brand-text {
    background: linear-gradient(135deg, var(--primary-color), var(--success-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.navbar-nav .nav-link {
    font-weight: 500;
    color: var(--text-color) !important;
    padding: 10px 15px !important;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
    background: var(--light-color);
}

.navbar-nav .nav-link i {
    margin-right: 5px;
}

.dropdown-menu {
    border: none;
    box-shadow: var(--shadow-lg);
    border-radius: 12px;
    padding: 10px 0;
    margin-top: 10px;
}

.dropdown-item {
    padding: 10px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background: var(--light-color);
    color: var(--primary-color);
}

.navbar-actions .btn {
    border-radius: 25px;
    padding: 8px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--success-color));
    border: none;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-outline-primary {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

/* ===== UTILITY CLASSES ===== */
.section-padding {
    padding: 80px 0;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 1rem;
    text-align: center;
}

.section-subtitle {
    font-size: 1.1rem;
    color: var(--text-muted);
    text-align: center;
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.card {
    border: none;
    border-radius: 16px;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.card-img-top {
    height: 250px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.card:hover .card-img-top {
    transform: scale(1.05);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .top-bar {
        text-align: center;
    }

    .top-bar .contact-info {
        margin-bottom: 10px;
    }

    .navbar-actions {
        margin-top: 15px;
    }

    .section-title {
        font-size: 2rem;
    }

    .section-padding {
        padding: 50px 0;
    }
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

.form-floating > .form-control-plaintext::placeholder, .form-floating > .form-control::placeholder {
  color: var(--bs-secondary-color);
  text-align: end;
}

.form-floating > .form-control-plaintext:focus::placeholder, .form-floating > .form-control:focus::placeholder {
  text-align: start;
}