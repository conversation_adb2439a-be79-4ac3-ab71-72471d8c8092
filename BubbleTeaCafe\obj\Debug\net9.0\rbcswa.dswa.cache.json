{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["YRfsNESRxoEunK/b3ZBDlkfMPlwXmuIr2HEn9q0SnuU=", "sF2MJCcscqbUrhoE/iLw46slOjwANR7vaX2l5ftBqIY=", "f9fJ/Gn5RKYZcOS6gAD55lyU0fwhCEDU6CABRsYQv1A=", "VTJ2HfqBd6TNhHvtjwNxX4Ywx2DMzNOrj61BZRYXXW0=", "PpC/kMLu0jn+4Hwd7zOCefgk5Q30WbM1PhrIa1jltSQ=", "HKFLFamcpGjGqLGuyBkSl3CS0eiH7Ymp+9VjSItH+4M=", "2YC2Niw7nY6lQOO2m+HMPKJjYhqWhaIEhKFUo3HUbnk=", "r29G1pskc/47Lm62kUN5uoClQGTcyCGpI5UXmQXkVng=", "6c6rpxT6lAeZ4STbh05CqQ8MURrQ8mh0NVZVRpRHqeA=", "V9y9GILHTZMDQeaZxZxHqW8wKyA1UznA0w9htXdCg/4=", "DlyskK6UPNQ4kgXfx28p8RPamcMozL+mAzF60by1Nd4=", "lDSS0BO/Cd+lz01TMI5JPyYffmHl2sci/VafP2YNZys=", "5hrhLL01GixmH0/c2CHZIUDhbb5nm5uqblPm/MhbySk=", "i/LyuHPrBXzi+I1gjnD1L0+YvpPasm4fpeEU52jxgwo=", "i/nB/qktZxLPDQCSVqD6jp29Lbi6EA911zdrocgxMj8=", "+5Cwe1XZ1ZtrRFX/vwY4vWJMXYA7o3e6yLOmpCygbas=", "7qgWY/UsEea6j6xC+IzC2qgwBXLmt9roTEO/nj0gv+w=", "RJFrKt+y+w0zBbwu23ckM3I1MflRmvH8rN6Od1IZjTk=", "XzYKxk6J90wRvtn+Kvw7M5u8Ua6oqwi7CR2YJ46qIDY=", "nsSEeO2WVXokRmUkQ9gFl7toyTmUTyi4O+xi6E7xca8=", "9nOsHDvj3L77v/xG0jbAk8hWBi6gI3/HEYXin+IaGfg=", "X4R0wglJGIeW3d3RaBvPeFGMG9EWH+UDF7K4D8dUKh0=", "6MSXlwSAAFuwWfLuP+zCNlKTyji865/2QOf0A/4ApO0=", "QmrmMLuPyM/Rd8IeRhE7kAijpQEl+/wM299cy75xrUA=", "tOttumiyHD2vUQllz6YZ1brmujW+nqTjGMGWuYaz7h0=", "A3E5vHVovXeOywva2P3n6cF/vKpF4jIA9Jl3yINdW6Y=", "ZsHufJCB6OjZQGeJzBmR9peDDeBLyiTsMadCX7piUrg=", "CUhGNcXE5gjUwOtf43vRo0acO3ptJWxCvRBHMUeE3Xs=", "5ddWiNT9JjH6mqUDQx/eadz8nB+NYHKAxBVNma71Tog=", "5DhXnX71Hk10A/uOh55hrRQOkZDaG2hFnpS+5RlL0xw=", "Z+I6+14tqIyVIlDVWS4CRco9FnGZcO9cP6zEIeX2swg=", "xQzkxaTlImI/XHJVaxfxKBpjWAt0FtQLl5il6c5HHwM=", "uj8c5GJQZw+MNjc/Qb94SulCQqJc3fOvr1h9uKpzFJM=", "GnTr8voX7wR6ZnZBxZH5farpdy5y2pB4viBNRPiqCiQ=", "k4IKOPLQ/9eAsdb1i+EttrVTehftK/o8rh0jw2e69Zw=", "YRy8CJzs6ul1qOTYaBc0mciUy9cnQB7JoP4n8EFH7EE=", "H1OzeTLwMSjx6oEoJRHEh1Xx3mZiFmRFmuyD5K7skxg=", "yGjAldCQBJr0w0svf+kGD9qywqHqxx8hJTMFSUfserw=", "5eT01HbYhwD+ihk35lK2QnyznY5iMnD7wQClOzeanQg=", "5OBYRBdo2trFBHIU0xgnoxyM/TfYZoJd12aGEBXu/Hs=", "tb9cNVfHv3AS/XlZDcVynVtec8cRTivUr88EzV2UQ90=", "xgmg//ZfobZ/btVel/FjqXs2qCIpQku/O9FEFw4PoHQ=", "6EZfbYPve9anFAe68EuWrMhU8Leb8tX1Jr4koAGMql4=", "/sYovreWlVfrw1elEsTBoxQRKJLLoMGYNbNuAF5PvQY=", "uzPBXewaRSGiGCig6evYywyxSYm3ifr+zxHNVTbQgyo=", "Noz0Daou3b1+peXkCiq41XflyIopn3/8Oj/RI8VHht0=", "eUcxC0nQvjm17rt9LXsUEdlnYPuGPhEXJOf2WmQuE9M=", "66Vnfjcn8kDM4Tgv+RTs5NYfVX1/d4tPBd+R/nA3cPY=", "0eGYUIma1yIbvvpZ54uiZ2LvSQHg8FDeMbafla7Dqek=", "MOoFZ5qmWs3AYpeTko6td7NCOHir/tNSkvMnny9b4FY=", "cb5Dm+uRQvH7vNOZA46I+SiAHM2gn0o1gypoGlytp2E=", "0M/8DSi/2lqWhtF3DBMdCn4fxwaGYXH/cgqmOgkYNeQ=", "WPpCEASKXKF7JQcezq7tnpN4xzsPCPQpZCVEXDYg2js=", "KEN4UWpNZb5EZU/NQ0eLgWoEoSSHijqTryPIkBKPqs4=", "7jAwJyC2qyTNuf256VnZKbW5/22wv9gubKIkJ03wrl0=", "oFa0JAHCO6ltiFvp4A/goGZ/STT4YfL2M4qK6Pp7rBs=", "y0yowVAZzV1KvjjxWwcPKSEi7Ef/I/0kqeUovT+GoLs=", "3Va1mL1ujbep8M4HZnvEqbZhwmymTvGo9MMqWfAdYag=", "pXlSCA3qtfBrpk1ArYbJlb7mf00TGjtZXTdr8PkjHd4=", "PGQP3RSLAhhWFoWX1LSCXIkXVF212F9NjZLTt8JBZ7w=", "8/aC4mbBpAA7CtsMcrlI4AbZ3pcqje9kmyGdluODTGs=", "Tnete/IfPmKK/u2BZk6CJGT/Svcm5QI3PMZw9Wk224Q=", "jtMylu4JovDez/IkP7AQzWJIPBm58CWmqbVxT5yx+JQ=", "IugGbplhqvM211mNVO8JDSsRkoFNpufu66csXlN4igE=", "3uA9n+2trPQewo1HGZ4Z3fQ6fWsCAHR+n1eUV3BWxMs="], "CachedAssets": {"YRfsNESRxoEunK/b3ZBDlkfMPlwXmuIr2HEn9q0SnuU=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\fwweffc7qb-5bpslw63bd.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "css/site#[.{fingerprint=5bpslw63bd}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6i8795xob9", "Integrity": "MuxoDw1Lu9C4l6aNAwdndRT9EY7AIUCygJB3Q2DEtZ0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\css\\site.css", "FileLength": 1893, "LastWriteTime": "2025-07-15T09:11:46.8451771+00:00"}, "sF2MJCcscqbUrhoE/iLw46slOjwANR7vaX2l5ftBqIY=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\5bmbc543ss-61n19gt1b8.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "favicon#[.{fingerprint=61n19gt1b8}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nezsohjtde", "Integrity": "wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\favicon.ico", "FileLength": 2468, "LastWriteTime": "2025-07-15T08:41:12.7348589+00:00"}, "f9fJ/Gn5RKYZcOS6gAD55lyU0fwhCEDU6CABRsYQv1A=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\p63nehuhnn-5ubxltd50m.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "images/create-placeholder#[.{fingerprint=5ubxltd50m}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\images\\create-placeholder.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nsqcqd9d5x", "Integrity": "WW+43Gd/xaLkcuwEIEnBsyK89ZBMtnVn/2MXFi2I8Zk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\images\\create-placeholder.html", "FileLength": 1222, "LastWriteTime": "2025-07-15T09:11:46.8451771+00:00"}, "PpC/kMLu0jn+4Hwd7zOCefgk5Q30WbM1PhrIa1jltSQ=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\5vttk2anq0-bqjiyaj88i.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=bqjiyaj88i}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-07-15T08:41:12.7564332+00:00"}, "HKFLFamcpGjGqLGuyBkSl3CS0eiH7Ymp+9VjSItH+4M=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\kh9j8y9nl7-c2jlpeoesf.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-07-15T08:41:12.7666969+00:00"}, "2YC2Niw7nY6lQOO2m+HMPKJjYhqWhaIEhKFUo3HUbnk=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\88h1kuz114-erw9l3u2r3.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=erw9l3u2r3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-07-15T08:41:12.7277524+00:00"}, "r29G1pskc/47Lm62kUN5uoClQGTcyCGpI5UXmQXkVng=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\ij9l7enzha-aexeepp0ev.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-07-15T08:41:12.7428572+00:00"}, "6c6rpxT6lAeZ4STbh05CqQ8MURrQ8mh0NVZVRpRHqeA=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\mrxn50kmib-d7shbmvgxk.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=d7shbmvgxk}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-07-15T08:41:12.7564332+00:00"}, "V9y9GILHTZMDQeaZxZxHqW8wKyA1UznA0w9htXdCg/4=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\pa3433fubv-ausgxo2sd3.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-07-15T08:41:12.7616907+00:00"}, "DlyskK6UPNQ4kgXfx28p8RPamcMozL+mAzF60by1Nd4=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\hhpxls5bgu-k8d9w2qqmf.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=k8d9w2qqmf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-07-15T08:41:12.7656979+00:00"}, "lDSS0BO/Cd+lz01TMI5JPyYffmHl2sci/VafP2YNZys=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\rd4oh1zqnu-cosvhxvwiu.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-07-15T08:41:12.7287535+00:00"}, "5hrhLL01GixmH0/c2CHZIUDhbb5nm5uqblPm/MhbySk=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\vdqrd19w0a-ub07r2b239.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=ub07r2b239}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-07-15T08:41:12.7317523+00:00"}, "i/LyuHPrBXzi+I1gjnD1L0+YvpPasm4fpeEU52jxgwo=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\fcdinv1xt2-fvhpjtyr6v.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-07-15T08:41:12.7388595+00:00"}, "i/nB/qktZxLPDQCSVqD6jp29Lbi6EA911zdrocgxMj8=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\knsq7nxox0-b7pk76d08c.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=b7pk76d08c}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-07-15T08:41:12.7388595+00:00"}, "+5Cwe1XZ1ZtrRFX/vwY4vWJMXYA7o3e6yLOmpCygbas=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\b9n9ymbcqz-fsbi9cje9m.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-07-15T08:41:12.7408586+00:00"}, "7qgWY/UsEea6j6xC+IzC2qgwBXLmt9roTEO/nj0gv+w=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\56b7atboyg-rzd6atqjts.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rzd6atqjts}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-07-15T08:41:12.7267511+00:00"}, "RJFrKt+y+w0zBbwu23ckM3I1MflRmvH8rN6Od1IZjTk=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\3us9vicbnq-ee0r1s7dh0.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-07-15T08:41:12.7307509+00:00"}, "XzYKxk6J90wRvtn+Kvw7M5u8Ua6oqwi7CR2YJ46qIDY=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\qdgkqeizi4-dxx9fxp4il.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=dxx9fxp4il}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-07-15T08:41:12.7307509+00:00"}, "nsSEeO2WVXokRmUkQ9gFl7toyTmUTyi4O+xi6E7xca8=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\cqpuynuli7-jd9uben2k1.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-07-15T08:41:12.7378593+00:00"}, "9nOsHDvj3L77v/xG0jbAk8hWBi6gI3/HEYXin+IaGfg=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\35m7m3uc1w-khv3u5hwcm.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=khv3u5hwcm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-07-15T08:41:12.7388595+00:00"}, "X4R0wglJGIeW3d3RaBvPeFGMG9EWH+UDF7K4D8dUKh0=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\x40n5sz1n0-r4e9w2rdcm.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-07-15T08:41:12.7338591+00:00"}, "6MSXlwSAAFuwWfLuP+zCNlKTyji865/2QOf0A/4ApO0=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\qjrkyr2pb2-lcd1t2u6c8.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=lcd1t2u6c8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-07-15T08:41:12.7378593+00:00"}, "QmrmMLuPyM/Rd8IeRhE7kAijpQEl+/wM299cy75xrUA=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\70dld4qim8-c2oey78nd0.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-07-15T08:41:12.739859+00:00"}, "tOttumiyHD2vUQllz6YZ1brmujW+nqTjGMGWuYaz7h0=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\r7k9sbuy6k-tdbxkamptv.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=tdbxkamptv}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-07-15T08:41:12.7408586+00:00"}, "A3E5vHVovXeOywva2P3n6cF/vKpF4jIA9Jl3yINdW6Y=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\f591s255p1-j5mq2jizvt.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-07-15T08:41:12.7616907+00:00"}, "ZsHufJCB6OjZQGeJzBmR9peDDeBLyiTsMadCX7piUrg=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\t0ro4obfkw-06098lyss8.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=06098lyss8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-07-15T08:41:12.7287535+00:00"}, "CUhGNcXE5gjUwOtf43vRo0acO3ptJWxCvRBHMUeE3Xs=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\b3bes3pkpo-nvvlpmu67g.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-07-15T08:41:12.7307509+00:00"}, "5ddWiNT9JjH6mqUDQx/eadz8nB+NYHKAxBVNma71Tog=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\40i7q8el12-s35ty4nyc5.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=s35ty4nyc5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-07-15T08:41:12.7438577+00:00"}, "5DhXnX71Hk10A/uOh55hrRQOkZDaG2hFnpS+5RlL0xw=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\fi73e751u1-pj5nd1wqec.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-07-15T08:41:12.770696+00:00"}, "Z+I6+14tqIyVIlDVWS4CRco9FnGZcO9cP6zEIeX2swg=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\aql27m497f-46ein0sx1k.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=46ein0sx1k}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-07-15T08:41:12.7752045+00:00"}, "xQzkxaTlImI/XHJVaxfxKBpjWAt0FtQLl5il6c5HHwM=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\bd17nyshu2-v0zj4ognzu.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-07-15T08:41:12.7358586+00:00"}, "uj8c5GJQZw+MNjc/Qb94SulCQqJc3fOvr1h9uKpzFJM=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\ybt304pxgv-37tfw0ft22.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=37tfw0ft22}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-07-15T08:41:12.7408586+00:00"}, "GnTr8voX7wR6ZnZBxZH5farpdy5y2pB4viBNRPiqCiQ=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\hsdz18hyfw-hrwsygsryq.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-07-15T08:41:12.7736976+00:00"}, "k4IKOPLQ/9eAsdb1i+EttrVTehftK/o8rh0jw2e69Zw=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\6stwktu1jn-pk9g2wxc8p.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=pk9g2wxc8p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-07-15T08:41:12.7595887+00:00"}, "YRy8CJzs6ul1qOTYaBc0mciUy9cnQB7JoP4n8EFH7EE=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\x285v136rd-ft3s53vfgj.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-07-15T08:41:12.7716974+00:00"}, "H1OzeTLwMSjx6oEoJRHEh1Xx3mZiFmRFmuyD5K7skxg=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\00utq5pbx5-6cfz1n2cew.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=6cfz1n2cew}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-07-15T08:41:12.7307509+00:00"}, "yGjAldCQBJr0w0svf+kGD9qywqHqxx8hJTMFSUfserw=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\mt3029ipch-6pdc2jztkx.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=6pdc2jztkx}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-07-15T08:41:12.7504337+00:00"}, "5eT01HbYhwD+ihk35lK2QnyznY5iMnD7wQClOzeanQg=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\jhqw0lpnj2-493y06b0oq.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=493y06b0oq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-07-15T08:41:12.7616907+00:00"}, "5OBYRBdo2trFBHIU0xgnoxyM/TfYZoJd12aGEBXu/Hs=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\1wj3uelf82-iovd86k7lj.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=iovd86k7lj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-07-15T08:41:12.7726986+00:00"}, "tb9cNVfHv3AS/XlZDcVynVtec8cRTivUr88EzV2UQ90=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\tifeqo7r1c-vr1egmr9el.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=vr1egmr9el}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-07-15T08:41:12.7773198+00:00"}, "xgmg//ZfobZ/btVel/FjqXs2qCIpQku/O9FEFw4PoHQ=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\1hr30lodgv-kbrnm935zg.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=kbrnm935zg}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-07-15T08:41:12.7338591+00:00"}, "6EZfbYPve9anFAe68EuWrMhU8Leb8tX1Jr4koAGMql4=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\fwtcigau4f-jj8uyg4cgr.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=jj8uyg4cgr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-07-15T08:41:12.7646986+00:00"}, "/sYovreWlVfrw1elEsTBoxQRKJLLoMGYNbNuAF5PvQY=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\75549f5oyv-y7v9cxd14o.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=y7v9cxd14o}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-07-15T08:41:12.7716974+00:00"}, "uzPBXewaRSGiGCig6evYywyxSYm3ifr+zxHNVTbQgyo=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\cxutck8z9s-notf2xhcfb.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=notf2xhcfb}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-07-15T08:41:12.7616907+00:00"}, "Noz0Daou3b1+peXkCiq41XflyIopn3/8Oj/RI8VHht0=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\ke9hsozesr-h1s4sie4z3.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=h1s4sie4z3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-07-15T08:41:12.7716974+00:00"}, "eUcxC0nQvjm17rt9LXsUEdlnYPuGPhEXJOf2WmQuE9M=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\kkyk6k0oxb-63fj8s7r0e.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=63fj8s7r0e}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-07-15T08:41:12.7307509+00:00"}, "66Vnfjcn8kDM4Tgv+RTs5NYfVX1/d4tPBd+R/nA3cPY=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\jnlh7sx20q-0j3bgjxly4.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=0j3bgjxly4}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-07-15T08:41:12.739859+00:00"}, "0eGYUIma1yIbvvpZ54uiZ2LvSQHg8FDeMbafla7Dqek=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\1oeal7qqkb-47otxtyo56.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint=47otxtyo56}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mrxpa<PERSON><PERSON><PERSON>", "Integrity": "8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-07-15T08:41:12.770696+00:00"}, "MOoFZ5qmWs3AYpeTko6td7NCOHir/tNSkvMnny9b4FY=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\doy9x1eoj7-4v8eqarkd7.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint=4v8eqarkd7}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw5m9o3hck", "Integrity": "ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-07-15T08:41:12.7595887+00:00"}, "cb5Dm+uRQvH7vNOZA46I+SiAHM2gn0o1gypoGlytp2E=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\3evekxe22k-356vix0kms.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint=356vix0kms}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-07-15T08:41:12.7666969+00:00"}, "0M/8DSi/2lqWhtF3DBMdCn4fxwaGYXH/cgqmOgkYNeQ=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\a471xplc8c-83jwlth58m.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint=83jwlth58m}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-07-15T08:41:12.7287535+00:00"}, "WPpCEASKXKF7JQcezq7tnpN4xzsPCPQpZCVEXDYg2js=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\81kbhefo5v-mrlpezrjn3.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint=mrlpezrjn3}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-07-15T08:41:12.729753+00:00"}, "KEN4UWpNZb5EZU/NQ0eLgWoEoSSHijqTryPIkBKPqs4=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\prh8aqr7sf-lzl9nlhx6b.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint=lzl9nlhx6b}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anlp8nykfw", "Integrity": "KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-07-15T08:41:12.7307509+00:00"}, "7jAwJyC2qyTNuf256VnZKbW5/22wv9gubKIkJ03wrl0=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\tv340g5b9d-ag7o75518u.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint=ag7o75518u}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgmochx07c", "Integrity": "T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-07-15T08:41:12.7368588+00:00"}, "oFa0JAHCO6ltiFvp4A/goGZ/STT4YfL2M4qK6Pp7rBs=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\ss6a58xtxg-x0q3zqp4vz.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint=x0q3zqp4vz}]?.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-07-15T08:41:12.7378593+00:00"}, "y0yowVAZzV1KvjjxWwcPKSEi7Ef/I/0kqeUovT+GoLs=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\3ns3qpxgxu-0i3buxo5is.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint=0i3buxo5is}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "slhp14mdwv", "Integrity": "YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 84431, "LastWriteTime": "2025-07-15T08:41:12.7338591+00:00"}, "3Va1mL1ujbep8M4HZnvEqbZhwmymTvGo9MMqWfAdYag=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\nm3evsuy0d-o1o13a6vjx.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=o1o13a6vjx}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4efnowp72v", "Integrity": "rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-07-15T08:41:12.7378593+00:00"}, "pXlSCA3qtfBrpk1ArYbJlb7mf00TGjtZXTdr8PkjHd4=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\1kr5y3g49k-ttgo8qnofa.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=ttgo8qnofa}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-07-15T08:41:12.7428572+00:00"}, "PGQP3RSLAhhWFoWX1LSCXIkXVF212F9NjZLTt8JBZ7w=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\g03h3o6qhc-2z0ns9nrw6.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint=2z0ns9nrw6}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlmiz42j7j", "Integrity": "9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-07-15T08:41:12.7438577+00:00"}, "8/aC4mbBpAA7CtsMcrlI4AbZ3pcqje9kmyGdluODTGs=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\vofk56aydg-muycvpuwrr.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=muycvpuwrr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trtvcfsjjx", "Integrity": "h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-07-15T08:41:12.757435+00:00"}, "Tnete/IfPmKK/u2BZk6CJGT/Svcm5QI3PMZw9Wk224Q=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\arcrh64dn3-87fc7y1x7t.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=87fc7y1x7t}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-07-15T08:41:12.7307509+00:00"}, "jtMylu4JovDez/IkP7AQzWJIPBm58CWmqbVxT5yx+JQ=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\6ib7ji3vp5-mlv21k5csn.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint=mlv21k5csn}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-07-15T08:41:12.7554386+00:00"}, "IugGbplhqvM211mNVO8JDSsRkoFNpufu66csXlN4igE=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\lhihnrvlt0-h9vxi7no3f.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Computed", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "BubbleTeaCafe#[.{fingerprint=h9vxi7no3f}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\scopedcss\\bundle\\BubbleTeaCafe.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mpkekj77wp", "Integrity": "WlDWUW6MEy1BnlrVFDuaIqHhEV0BxfPvgTfPMZ81xZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\scopedcss\\bundle\\BubbleTeaCafe.styles.css", "FileLength": 545, "LastWriteTime": "2025-07-15T08:41:12.7763193+00:00"}, "3uA9n+2trPQewo1HGZ4Z3fQ6fWsCAHR+n1eUV3BWxMs=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\fmv4low3a0-h9vxi7no3f.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Computed", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "BubbleTeaCafe#[.{fingerprint=h9vxi7no3f}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\BubbleTeaCafe.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mpkekj77wp", "Integrity": "WlDWUW6MEy1BnlrVFDuaIqHhEV0BxfPvgTfPMZ81xZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\BubbleTeaCafe.bundle.scp.css", "FileLength": 545, "LastWriteTime": "2025-07-15T08:41:12.7554386+00:00"}, "VTJ2HfqBd6TNhHvtjwNxX4Ywx2DMzNOrj61BZRYXXW0=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\v35wrnwx99-nfa962lllj.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "js/site#[.{fingerprint=nfa962lllj}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fn2js6bfja", "Integrity": "1Yfu4C5DDeK3Zhgc153FhFg2jn4WP3aIo5zb7+8uV8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\js\\site.js", "FileLength": 1742, "LastWriteTime": "2025-07-15T09:11:46.8461838+00:00"}}, "CachedCopyCandidates": {}}