<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Admin Panel</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet" />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --admin-primary: #2563eb;
            --admin-secondary: #64748b;
            --admin-success: #059669;
            --admin-danger: #dc2626;
            --admin-warning: #d97706;
            --admin-info: #0891b2;
            --admin-dark: #1e293b;
            --admin-light: #f8fafc;
            --sidebar-width: 280px;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--admin-light);
        }
        
        .admin-sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background: linear-gradient(135deg, var(--admin-dark) 0%, #334155 100%);
            z-index: 1000;
            transition: all 0.3s ease;
            overflow-y: auto;
        }
        
        .admin-sidebar .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .admin-sidebar .sidebar-brand {
            color: white;
            text-decoration: none;
            font-size: 1.25rem;
            font-weight: 600;
        }
        
        .admin-sidebar .sidebar-brand:hover {
            color: #60a5fa;
        }
        
        .admin-nav {
            padding: 1rem 0;
        }
        
        .admin-nav-item {
            margin: 0.25rem 1rem;
        }
        
        .admin-nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            color: #cbd5e1;
            text-decoration: none;
            border-radius: 0.5rem;
            transition: all 0.2s ease;
            font-weight: 500;
        }
        
        .admin-nav-link:hover {
            background-color: rgba(255,255,255,0.1);
            color: white;
        }
        
        .admin-nav-link.active {
            background-color: var(--admin-primary);
            color: white;
        }
        
        .admin-nav-link i {
            width: 20px;
            margin-right: 0.75rem;
        }
        
        .admin-main {
            margin-left: var(--sidebar-width);
            min-height: 100vh;
        }
        
        .admin-header {
            background: white;
            padding: 1rem 2rem;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .admin-content {
            padding: 2rem;
        }
        
        .admin-card {
            background: white;
            border-radius: 0.75rem;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border: 1px solid #e2e8f0;
        }
        
        .stats-card {
            background: white;
            border-radius: 0.75rem;
            padding: 1.5rem;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border-left: 4px solid var(--admin-primary);
        }
        
        .user-dropdown {
            position: relative;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--admin-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            cursor: pointer;
        }
        
        @media (max-width: 768px) {
            .admin-sidebar {
                transform: translateX(-100%);
            }
            
            .admin-sidebar.show {
                transform: translateX(0);
            }
            
            .admin-main {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="admin-sidebar" id="adminSidebar">
        <div class="sidebar-header">
            <a href="/Admin" class="sidebar-brand">
                <i class="fas fa-coffee me-2"></i>
                Admin Panel
            </a>
        </div>
        
        <nav class="admin-nav">
            <div class="admin-nav-item">
                <a href="/Admin" class="admin-nav-link">
                    <i class="fas fa-tachometer-alt"></i>
                    Dashboard
                </a>
            </div>
            
            <div class="admin-nav-item">
                <a href="/Admin/Products" class="admin-nav-link">
                    <i class="fas fa-box"></i>
                    Quản Lý Sản Phẩm
                </a>
            </div>
            
            <div class="admin-nav-item">
                <a href="/Admin/Orders" class="admin-nav-link">
                    <i class="fas fa-shopping-cart"></i>
                    Quản Lý Đơn Hàng
                </a>
            </div>
            
            <div class="admin-nav-item">
                <a href="/Admin/Users" class="admin-nav-link">
                    <i class="fas fa-users"></i>
                    Quản Lý Người Dùng
                </a>
            </div>
            
            <div class="admin-nav-item">
                <a href="/Admin/Categories" class="admin-nav-link">
                    <i class="fas fa-tags"></i>
                    Danh Mục
                </a>
            </div>
            
            <div class="admin-nav-item">
                <a href="/Admin/Reports" class="admin-nav-link">
                    <i class="fas fa-chart-bar"></i>
                    Báo Cáo
                </a>
            </div>
            
            <div class="admin-nav-item">
                <a href="/Admin/Settings" class="admin-nav-link">
                    <i class="fas fa-cogs"></i>
                    Cài Đặt
                </a>
            </div>
            
            <hr style="border-color: rgba(255,255,255,0.1); margin: 1rem;">
            
            <div class="admin-nav-item">
                <a href="/" class="admin-nav-link" target="_blank">
                    <i class="fas fa-external-link-alt"></i>
                    Xem Website
                </a>
            </div>
            
            <div class="admin-nav-item">
                <a href="/Admin/Logout" class="admin-nav-link text-danger">
                    <i class="fas fa-sign-out-alt"></i>
                    Đăng Xuất
                </a>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="admin-main">
        <!-- Header -->
        <header class="admin-header">
            <div class="d-flex align-items-center">
                <button class="btn btn-link d-md-none me-3" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <h1 class="h4 mb-0">@ViewData["Title"]</h1>
            </div>
            
            <div class="d-flex align-items-center">
                <div class="me-3">
                    <small class="text-muted">Xin chào,</small>
                    <strong>Admin</strong>
                </div>
                <div class="user-dropdown">
                    <div class="user-avatar" data-bs-toggle="dropdown">
                        A
                    </div>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="/Admin/Profile"><i class="fas fa-user me-2"></i>Hồ Sơ</a></li>
                        <li><a class="dropdown-item" href="/Admin/Settings"><i class="fas fa-cogs me-2"></i>Cài Đặt</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="/Admin/Logout"><i class="fas fa-sign-out-alt me-2"></i>Đăng Xuất</a></li>
                    </ul>
                </div>
            </div>
        </header>

        <!-- Content -->
        <main class="admin-content">
            @RenderBody()
        </main>
    </div>

    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    
    <script>
        function toggleSidebar() {
            document.getElementById('adminSidebar').classList.toggle('show');
        }
        
        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(e) {
            const sidebar = document.getElementById('adminSidebar');
            const toggleBtn = e.target.closest('[onclick="toggleSidebar()"]');
            
            if (!sidebar.contains(e.target) && !toggleBtn && window.innerWidth <= 768) {
                sidebar.classList.remove('show');
            }
        });
    </script>
    
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
