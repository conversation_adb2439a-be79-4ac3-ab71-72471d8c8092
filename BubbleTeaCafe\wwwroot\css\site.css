html {
  font-size: 14px;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

html {
  position: relative;
  min-height: 100%;
}

body {
  margin-bottom: 60px;
}

.form-floating > .form-control-plaintext::placeholder, .form-floating > .form-control::placeholder {
  color: var(--bs-secondary-color);
  text-align: end;
}

.form-floating > .form-control-plaintext:focus::placeholder, .form-floating > .form-control:focus::placeholder {
  text-align: start;
}

/* Custom styles for Bubble Tea & Coffee Shop */
:root {
  --primary-color: #667eea;
  --secondary-color: #764ba2;
  --success-color: #28a745;
  --warning-color: #ffc107;
  --danger-color: #dc3545;
  --info-color: #17a2b8;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
  --shadow-light: 0 2px 8px rgba(0,0,0,0.1);
  --shadow-medium: 0 4px 15px rgba(0,0,0,0.15);
  --shadow-heavy: 0 8px 25px rgba(0,0,0,0.2);
  --border-radius: 12px;
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Body improvements */
body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: var(--dark-color);
}

/* Navbar improvements */
.navbar {
  backdrop-filter: blur(10px);
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
  box-shadow: var(--shadow-light);
  transition: var(--transition);
}

.navbar-brand {
  font-weight: 700;
  font-size: 1.5rem;
  transition: var(--transition);
}

.navbar-brand:hover {
  transform: scale(1.05);
}

.nav-link {
  font-weight: 500;
  transition: var(--transition);
  position: relative;
}

.nav-link::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: 0;
  left: 50%;
  background-color: white;
  transition: var(--transition);
}

.nav-link:hover::after {
  width: 100%;
  left: 0;
}

/* Product card improvements */
.product-card {
  transition: var(--transition);
  border: none;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  overflow: hidden;
  background: white;
}

.product-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-heavy);
}

.product-image {
  height: 220px;
  object-fit: cover;
  transition: var(--transition);
}

.product-card:hover .product-image {
  transform: scale(1.1);
}

.product-image-placeholder {
  height: 220px;
  background: linear-gradient(45deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: var(--border-radius) var(--border-radius) 0 0;
  transition: var(--transition);
}

.product-card:hover .product-image-placeholder {
  background: linear-gradient(45deg, #e9ecef 0%, #dee2e6 100%);
}

.price-tag {
  font-size: 1.3rem;
  font-weight: 700;
  color: var(--success-color);
  text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

/* Button improvements */
.btn {
  border-radius: 8px;
  font-weight: 500;
  transition: var(--transition);
  border: none;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: var(--transition);
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  box-shadow: var(--shadow-light);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.btn-outline-primary {
  border: 2px solid var(--primary-color);
  color: var(--primary-color);
}

.btn-outline-primary:hover {
  background: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

/* Section improvements */
.category-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  color: white;
  padding: 3rem 0;
  margin-bottom: 2rem;
  box-shadow: var(--shadow-medium);
  position: relative;
  overflow: hidden;
}

.category-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('/images/pattern.png');
  opacity: 0.1;
}

.hero-section {
  background: linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.3)), url('/images/hero-bg.svg');
  background-size: cover;
  background-position: center;
  color: white;
  padding: 7rem 0;
  text-align: center;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-medium);
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, transparent 20%, rgba(0,0,0,0.2) 100%);
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
  opacity: 0.1;
  animation: gradientShift 10s ease-in-out infinite;
}

.min-vh-75 {
  min-height: 75vh;
}

.text-shadow {
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.hero-icon-container {
  position: relative;
  display: inline-block;
}

.hero-icon {
  font-size: 15rem;
  opacity: 0.8;
  animation: float 3s ease-in-out infinite;
  color: rgba(255,255,255,0.9);
}

.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.floating-element {
  position: absolute;
  font-size: 2rem;
  animation: floatAround 8s linear infinite;
  opacity: 0.7;
}

.floating-element:nth-child(1) {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.floating-element:nth-child(2) {
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.floating-element:nth-child(3) {
  bottom: 30%;
  left: 20%;
  animation-delay: 4s;
}

.floating-element:nth-child(4) {
  top: 40%;
  right: 30%;
  animation-delay: 6s;
}

.pulse-animation {
  animation: pulse 2s infinite;
}

/* Animation keyframes */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes floatAround {
  0% { transform: translateY(0px) rotate(0deg); }
  25% { transform: translateY(-10px) rotate(90deg); }
  50% { transform: translateY(0px) rotate(180deg); }
  75% { transform: translateY(-10px) rotate(270deg); }
  100% { transform: translateY(0px) rotate(360deg); }
}

@keyframes gradientShift {
  0%, 100% { background: linear-gradient(45deg, var(--primary-color), var(--secondary-color)); }
  50% { background: linear-gradient(45deg, var(--secondary-color), var(--primary-color)); }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* Cart badge */
.cart-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  transition: var(--transition);
}

/* Footer improvements */
.footer {
  margin-top: auto;
  background-color: var(--light-color);
  box-shadow: 0 -2px 10px rgba(0,0,0,0.05);
}

/* Product options */
.product-options {
  background-color: var(--light-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  margin: 1.5rem 0;
  box-shadow: var(--shadow-light);
  transition: var(--transition);
}

.product-options:hover {
  box-shadow: var(--shadow-medium);
}

.option-group {
  margin-bottom: 1.5rem;
}

.option-group label {
  font-weight: 600;
  color: var(--dark-color);
}

/* Card improvements */
.card {
  border-radius: var(--border-radius);
  border: none;
  box-shadow: var(--shadow-light);
  transition: var(--transition);
  overflow: hidden;
}

.card:hover {
  box-shadow: var(--shadow-medium);
}

.card-header {
  border-bottom: none;
  background-color: white;
  font-weight: 600;
}

/* Form improvements */
.form-control, .form-select {
  border-radius: 8px;
  border: 1px solid #dee2e6;
  padding: 0.6rem 1rem;
  transition: var(--transition);
}

.form-control:focus, .form-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.25);
}

.form-check-input:checked {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

/* Loading spinner */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255,255,255,.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
}

/* Smooth page transitions */
.page-transition {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.3s ease-out;
}

.page-transition.loaded {
  opacity: 1;
  transform: translateY(0);
}

/* Enhanced card hover effects */
.card-hover-effect {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover-effect:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

/* Smooth scroll indicator */
.scroll-indicator {
  position: fixed;
  top: 0;
  left: 0;
  width: 0%;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  z-index: 9999;
  transition: width 0.3s ease;
}

/* Animation keyframes */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

@keyframes slideInLeft {
  from { opacity: 0; transform: translateX(-50px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes slideInRight {
  from { opacity: 0; transform: translateX(50px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes bounceIn {
  0% { opacity: 0; transform: scale(0.3); }
  50% { opacity: 1; transform: scale(1.05); }
  70% { transform: scale(0.9); }
  100% { opacity: 1; transform: scale(1); }
}

.fade-in {
  animation: fadeIn 0.8s ease-out forwards;
}

.slide-in-left {
  animation: slideInLeft 0.8s ease-out forwards;
}

.slide-in-right {
  animation: slideInRight 0.8s ease-out forwards;
}

.bounce-in {
  animation: bounceIn 0.8s ease-out forwards;
}

/* Responsive improvements */
@media (max-width: 768px) {
  .hero-section {
    padding: 5rem 0;
  }

  .product-card {
    margin-bottom: 1.5rem;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}