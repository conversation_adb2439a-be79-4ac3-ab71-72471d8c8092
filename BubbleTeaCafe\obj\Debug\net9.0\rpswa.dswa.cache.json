{"GlobalPropertiesHash": "/zmfgOUhrhhZ1G6TddYnEe6dzQwgC4eL3sCbN5K2STs=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["WGgOtT8u78it3iZpMgnJKqcO4i2azheTf6BtVe6qCNU=", "y4iwwMLQL7UCup/lq+fK3pOms4HMqJnttUm1flhcotc=", "h+rVAuLsZZTwL8DbmgKZtWYHvLeHZeAgN4akfEzDynQ=", "oXobXKs0RtCQmhHu0Rut5HryejrFkLYzXfeqcVLKGGI=", "ml9iMITj5J8YMqJM7xjKcAjd10BRxdstyIfJHXOf8Lo=", "JGlfyLcuSz6gKv8kQX+oyCC2TOyPqmELjnNc10kbaiU=", "WGS7DAoWfNq9QNbwoVAddj+Q5GMgZw7klkwwkMYL+ww=", "DJ89P84AfiyysKo8ENmNXlEtJj44mSWJGYKDeQWbqfU=", "z3HcRGl40ZcUTQnI4Hpl4oIf0u4ZpvOLaa3VHGIP1lw=", "VUFpf4CG0NWFwsLls5o1DM9EsVCR3AHse7jefs4KRWE=", "R1ysDv2c+gZqBUpWgNIadBbxWg39bf7V76N7eAUGWkI=", "RlYDU4K1XS7FBN91upfyxWrn9BzA7RLlETfDcbhI628=", "yHsKnOPYHXAqmYhdZJZlnlVWqA9no9a/PoAGt6qlU/o=", "o9wLkx6xidQxJ/xS/vOG8j+xox0XK7/CcO/HbtrkBH0=", "kXV95kGkBejNU3l+vODB/eAapZ9QU1PsQZ9OBwAyxkY=", "NbkeMTA39tvW8+nusvz61l5+o+qGbxaBvtgFsFfB8kY=", "M5bOw4tIedGmE6fn5CZKbIytlDCWkWOLMWsVc/OeL9g=", "fvOvEXTMnaZJDnVCzFoWfUYNJgHve/XfbDGXkZDgZpo=", "zGK9LdDJCIcuFkS1M/gnRS7/xa0CxxedAyzAmPJwmmA=", "qsapzgxb7VCripz8fzZwLLK6dvafvtnncr+OKO6dSHM=", "t911A9VioEnms9QAPyaY9mnyvZeqRMFcWox0t82g+RU=", "UJ1e3xV1RJELzLwLwLJQKweR4l5CtRRexVKo2qo2Xqc=", "okBjlYhxh92gF+fWST1XfxDCjwj+hu1rgssOf5acEmI=", "bnR0RkH+Mbxp2nEoTWCcrunlCYsvLSj7mVrrJSZK9BU=", "nMBoJNW2sZJ6hp4RTbqTLteJS4ZtNRZetfZJlPsMbLk=", "inFIiPUWqRRLOzrnPjhT+aDXQW0f9wbI0IC3i8nv1LM=", "51vqZeAJ9JCRDUxsoY1A4IIdy2exaUlAg8Aftm3yFII=", "T0gKP/DU6gEs8Vb0Mg33gHAHy+mWAz0pMYhjaLlr5k4=", "aOHi1QULZisG760WKMINaPZkDMDyx2XYLprX6LDVTOI=", "9CNJaVAlTbEdE53cwPpTHgs4WT1kKyQwpgv42JS68Og=", "JYHoBaO9O4wT98znSl+0/qBRSc457WKFPQMR/3y7Mp8=", "+Pu0fknpJNOmoFHBH1gCoiV8cJc+o8tnfLJsq0+pFFU=", "9iiTosGL+W9zgMX7Mk06CPHDn+Y8DWTsaxmFs3iYrKQ=", "wEwMlkeUEbY52oUMho6Z/76V3wmRLnONdbiNhqbogY4=", "E+y1t/nkUhOCroaFahoZAOG//3VNpcMpBWTWXF71thQ=", "YdbTlAZ9/5s1aTWh49wdiHAgQBs2uNoklQiB/LTLqxg=", "61R8o7iPk9vLzoaM98N9YEDzP2M7oT283Mo4jGCSJRs=", "2oui82CqM9eIicNMYefUKDA1+WsTqGwwmf0KgiKhqs8=", "v5eeHyNL9UaiMTw9qZF3Fmv/gKpRHaiEWDOzo3L58ts=", "c1zP2VPbCbWOHGW1Ovp30fdpEJZ0GiCEOsQa92y35J0=", "kkdULGiK7coWOCzIP4v0fgpMS2gCrKfLiwWe2voRRUY=", "gaZpqRfuChj6zEwEXUizvT83yNbUYntf60ZN92SdcaI=", "aNx8jIKiyFhoMXXPYCCkK0b4sqTzOeNOvYsXSJyjZbE=", "1BtMz5Rg/6mvb/hj+tDhCFcjeR38jeiIs58w467Gkx8=", "aaD7kOcqoAsngZJE/Usph0ouAfOQ295U3M9003VH1O0=", "eEORow1n7Sl17NyQMuwNhchSw6ARd2aGZ9JoDF00CRQ=", "I5pnxHmx9kY/29ED9AhuMqUhm4PjK5X/HeWjMJj+Njo=", "ieKr5+lMmeRwG+/UtVUpOkpBRgFGgnMmdGp8OD+1gBw=", "sk2KXAx+aIVg0G7r19to648ZrX74HbdtlC768njfJZY=", "meOlixs9HUtezcEdKkELpCYBtj1KO2Py1+RJETIH3X0=", "sNdCHquIQqZuo+spVqY6LebCAhzmjm2jL+2o63ldoZg=", "93cblINvw2Na21uGb+1Dwdm6IOOu7IoigYsJ2P4MQjg=", "6znRZ5t1hIvh5Xg7hc3aJI7cb0qyw5qNH+z2qUtnic0=", "+IVGYVP4RozvndxaqM/NG/Ep0gMMTfRayYH9KgwNya4=", "iSJIQRFRLYmblGMmXmUz0WJ+UDHfwddknCKwg3Kd42Y=", "KseCE4v1Ip9gSrQZeEhjgxRSa+z64L0d761LSMgDnpI=", "Xhgn1BBCwLm7iTOsvJDiSbpx9vtz+lvTf2hUZk+tULc=", "h4g6+dT/n+FPSErnH1yIlqeFJG1qoXZW/uATG+r5w9A=", "M70qbuzjStmkOP5rKgEbN7y0yxhtXJXRCSk166umZSE=", "dSV+pWx5FErJC9T+Bv1fXbLxqNpw16bWfqi5hU5pjUo=", "qRnXKmecWx96LxU/JCUwqOdiyUJmWuMtRGuTki79+vY=", "DZB8OVfPzjVOn0Ci2xKfdJFL1EpmWTdCn7NUXWQsGSQ=", "5TOdmgqxfDaUmOHYLpciP24yDXKXj8d+VKNSoHrdt/I=", "PWl97AChSjZLUW5WvWXIMw5r9zhsTBOCuDvlP7hKCsY=", "eqoRzaO759u6bEKkrtEYGvzxzuEzkADPQtdnLAtV73A=", "r8/tioIf3sKNE8RdKx3ohHJPyvpL4WHeDUK8sjdTBVU=", "PrFMtmZFm2oB659G7rafDELTDAd16furBqRBLspO66w=", "3M/2gY4LNzJpCl1KNpEFWuCvk+hhZEGrJudPPUCncgk=", "ANeXpHFJAeDIX/CFA+bSESkNzEwTVaMlyDUhqQlqQiI=", "A6Fcw7XEHb5d8yVsbJBLfImUUxAmWBaXc7DGyhJz5S4=", "6JNFIz7mJ+tQHXGicxcN3Au0zcqV6xEStWCkerPFD+4=", "6aTE0BMMc1KRe5Ma1IOkjsRGhKm36zS7xy/qsa9CvVY=", "xFgwIDt/VJXDf77AiVu9on3h35EyHnjpd0neS3Ja6ck=", "fj9OB+7zoZgrEbEh/lScX0OX58wy7NzAQki8MZ5K9XA=", "+XxVc/j6SR64QXk/pY6Wo6gNyNPt+vr8WTjon36CuUg=", "8rry7UVJ7Lc2ZmMz/h4aa3k8dnbjx8mUS6HNayOT8UY=", "kpksjl3FzOEcuDau7e66aVGX61DhsBxLreUbbQVSXK8=", "M1rzZge/iWB02BLImUO5hwUPGWmPKRPjryRBkDNNnRo=", "iGuip4taGqvpSfU0/WbZ5o+66eLP5ByO7HjBrJDaKvk=", "GZRn4CbafWznqYzICuEUK2wwg+L4f530qzOLlxEeda0=", "PX7JISac2IiNVqF4ohG3CzsvlVmcOhLAcruLCoZLgqY=", "xuTQ4CSFPj7nnuCbpGjfXOnaHptfErwi/CSBhdKCxyQ=", "4y+Xq1lpvIMPYK5nh7hREXBwHWKyMs7GYlEf3jLgnQ0=", "77P2hJsaTdh2D5FKac268Di/JzK3wJheLlhRyI3dOrM=", "yjHu8Pu7Jn0YnkMbffhlL0fT2ZacV6IxCAxsoiANqPw=", "E1S1iHI8dNlQpQHc4g1srwGnnTsbmdziHl8nRRcda5E=", "EL4FmeVlWiCZoyQSOqxr3eR6msOYwUf9wou3FLcw8ns=", "SKJeu6tutH+SMyVOHJWKGsnew0cwEYJqlmu/03L1Hj8=", "pBQ6tUgeELDwIvzzNHZOqSCMTwsMam5eVVf7oUM+L3A=", "lRvTuLb3s66K3g1bNbzfOr41vRsbF1FsHij6Ms1j7Gw=", "uF6ns2p/ZAumS0YSjp2x5C/IFF5Xwy1XzKh1KJSaA88=", "LfXIhQ0q34TadOyresDhWzbVUJTHOP4BzNy7lnRzXh4=", "ECDU984XiZLgWbpePPJPwxorQo6p1OuTjkIlcxZJAcw=", "FMDh09I3HLlrDHq9GhYqDaU2t6SFZkErYEZBAo9X4Pw=", "tE6J8gjWzni1ZmyBKAAT/8jWEbtMUC2olMFquDat5mY="], "CachedAssets": {"JGlfyLcuSz6gKv8kQX+oyCC2TOyPqmELjnNc10kbaiU=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\images\\create-placeholder.html", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "images/create-placeholder#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5ubxltd50m", "Integrity": "LZOp9BdYnD5wBNVc7ZTFkAE3Gw6hgXaz/5F9/t6Lmkg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\create-placeholder.html", "FileLength": 4745, "LastWriteTime": "2025-07-15T09:05:53.7730674+00:00"}, "h+rVAuLsZZTwL8DbmgKZtWYHvLeHZeAgN4akfEzDynQ=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\favicon.ico", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 5430, "LastWriteTime": "2025-07-15T08:12:28.3361947+00:00"}, "ml9iMITj5J8YMqJM7xjKcAjd10BRxdstyIfJHXOf8Lo=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\images\\ca-phe-sua-da.svg", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "images/ca-phe-sua-da#[.{fingerprint}]?.svg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j5wy8axj4k", "Integrity": "uejbIQrGTJelFa0Vb5gN6vFcsH7lAVCJYqefkWnC4AU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\ca-phe-sua-da.svg", "FileLength": 2993, "LastWriteTime": "2025-07-15T10:00:29.4227756+00:00"}, "kXV95kGkBejNU3l+vODB/eAapZ9QU1PsQZ9OBwAyxkY=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bqjiyaj88i", "Integrity": "Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70329, "LastWriteTime": "2025-07-15T08:12:28.2236861+00:00"}, "NbkeMTA39tvW8+nusvz61l5+o+qGbxaBvtgFsFfB8kY=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2j<PERSON><PERSON><PERSON><PERSON>", "Integrity": "xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 203221, "LastWriteTime": "2025-07-15T08:12:28.2238857+00:00"}, "M5bOw4tIedGmE6fn5CZKbIytlDCWkWOLMWsVc/OeL9g=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "erw9l3u2r3", "Integrity": "5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51795, "LastWriteTime": "2025-07-15T08:12:28.2238857+00:00"}, "fvOvEXTMnaZJDnVCzFoWfUYNJgHve/XfbDGXkZDgZpo=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "aexeepp0ev", "Integrity": "kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 115986, "LastWriteTime": "2025-07-15T08:12:28.2248917+00:00"}, "zGK9LdDJCIcuFkS1M/gnRS7/xa0CxxedAyzAmPJwmmA=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d7shbmvgxk", "Integrity": "CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70403, "LastWriteTime": "2025-07-15T08:12:28.2248917+00:00"}, "qsapzgxb7VCripz8fzZwLLK6dvafvtnncr+OKO6dSHM=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ausgxo2sd3", "Integrity": "/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 203225, "LastWriteTime": "2025-07-15T08:12:28.2268935+00:00"}, "t911A9VioEnms9QAPyaY9mnyvZeqRMFcWox0t82g+RU=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "k8d9w2qqmf", "Integrity": "vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51870, "LastWriteTime": "2025-07-15T08:12:28.2278933+00:00"}, "UJ1e3xV1RJELzLwLwLJQKweR4l5CtRRexVKo2qo2Xqc=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cosvhxvwiu", "Integrity": "7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 116063, "LastWriteTime": "2025-07-15T08:12:28.2288928+00:00"}, "okBjlYhxh92gF+fWST1XfxDCjwj+hu1rgssOf5acEmI=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ub07r2b239", "Integrity": "lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 12065, "LastWriteTime": "2025-07-15T08:12:28.2288928+00:00"}, "bnR0RkH+Mbxp2nEoTWCcrunlCYsvLSj7mVrrJSZK9BU=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fvhpjtyr6v", "Integrity": "RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 129371, "LastWriteTime": "2025-07-15T08:12:28.2298922+00:00"}, "nMBoJNW2sZJ6hp4RTbqTLteJS4ZtNRZetfZJlPsMbLk=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7pk76d08c", "Integrity": "l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 10126, "LastWriteTime": "2025-07-15T08:12:28.2298922+00:00"}, "inFIiPUWqRRLOzrnPjhT+aDXQW0f9wbI0IC3i8nv1LM=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fsbi9cje9m", "Integrity": "0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 51369, "LastWriteTime": "2025-07-15T08:12:28.230893+00:00"}, "51vqZeAJ9JCRDUxsoY1A4IIdy2exaUlAg8Aftm3yFII=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rzd6atqjts", "Integrity": "V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 12058, "LastWriteTime": "2025-07-15T08:12:28.230893+00:00"}, "T0gKP/DU6gEs8Vb0Mg33gHAHy+mWAz0pMYhjaLlr5k4=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ee0r1s7dh0", "Integrity": "OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 129386, "LastWriteTime": "2025-07-15T08:12:28.2318924+00:00"}, "aOHi1QULZisG760WKMINaPZkDMDyx2XYLprX6LDVTOI=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dxx9fxp4il", "Integrity": "/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 10198, "LastWriteTime": "2025-07-15T08:12:28.2318924+00:00"}, "9CNJaVAlTbEdE53cwPpTHgs4WT1kKyQwpgv42JS68Og=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jd9uben2k1", "Integrity": "910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 63943, "LastWriteTime": "2025-07-15T08:12:28.2318924+00:00"}, "JYHoBaO9O4wT98znSl+0/qBRSc457WKFPQMR/3y7Mp8=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "khv3u5hwcm", "Integrity": "2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 107823, "LastWriteTime": "2025-07-15T08:12:28.2328912+00:00"}, "+Pu0fknpJNOmoFHBH1gCoiV8cJc+o8tnfLJsq0+pFFU=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r4e9w2rdcm", "Integrity": "Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 267535, "LastWriteTime": "2025-07-15T08:12:28.2368939+00:00"}, "9iiTosGL+W9zgMX7Mk06CPHDn+Y8DWTsaxmFs3iYrKQ=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lcd1t2u6c8", "Integrity": "KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 85352, "LastWriteTime": "2025-07-15T08:12:28.2378947+00:00"}, "wEwMlkeUEbY52oUMho6Z/76V3wmRLnONdbiNhqbogY4=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2oey78nd0", "Integrity": "rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 180381, "LastWriteTime": "2025-07-15T08:12:28.2378947+00:00"}, "E+y1t/nkUhOCroaFahoZAOG//3VNpcMpBWTWXF71thQ=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tdbxkamptv", "Integrity": "H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 107691, "LastWriteTime": "2025-07-15T08:12:28.2388956+00:00"}, "YdbTlAZ9/5s1aTWh49wdiHAgQBs2uNoklQiB/LTLqxg=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j5mq2jizvt", "Integrity": "p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 267476, "LastWriteTime": "2025-07-15T08:12:28.2398934+00:00"}, "61R8o7iPk9vLzoaM98N9YEDzP2M7oT283Mo4jGCSJRs=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "06098lyss8", "Integrity": "GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 85281, "LastWriteTime": "2025-07-15T08:12:28.2411483+00:00"}, "2oui82CqM9eIicNMYefUKDA1+WsTqGwwmf0KgiKhqs8=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvvlpmu67g", "Integrity": "o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 180217, "LastWriteTime": "2025-07-15T08:12:28.2411483+00:00"}, "v5eeHyNL9UaiMTw9qZF3Fmv/gKpRHaiEWDOzo3L58ts=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "s35ty4nyc5", "Integrity": "GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 281046, "LastWriteTime": "2025-07-15T08:12:28.2421606+00:00"}, "c1zP2VPbCbWOHGW1Ovp30fdpEJZ0GiCEOsQa92y35J0=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pj5nd1wqec", "Integrity": "KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 679755, "LastWriteTime": "2025-07-15T08:12:28.2475695+00:00"}, "kkdULGiK7coWOCzIP4v0fgpMS2gCrKfLiwWe2voRRUY=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "46ein0sx1k", "Integrity": "PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 232803, "LastWriteTime": "2025-07-15T08:12:28.2485718+00:00"}, "gaZpqRfuChj6zEwEXUizvT83yNbUYntf60ZN92SdcaI=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v0zj4ognzu", "Integrity": "8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 589892, "LastWriteTime": "2025-07-15T08:12:28.2505726+00:00"}, "aNx8jIKiyFhoMXXPYCCkK0b4sqTzOeNOvYsXSJyjZbE=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "37tfw0ft22", "Integrity": "j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 280259, "LastWriteTime": "2025-07-15T08:12:28.2515722+00:00"}, "1BtMz5Rg/6mvb/hj+tDhCFcjeR38jeiIs58w467Gkx8=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hrwsygsryq", "Integrity": "3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 679615, "LastWriteTime": "2025-07-15T08:12:28.254573+00:00"}, "aaD7kOcqoAsngZJE/Usph0ouAfOQ295U3M9003VH1O0=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pk9g2wxc8p", "Integrity": "h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 232911, "LastWriteTime": "2025-07-15T08:12:28.2555718+00:00"}, "eEORow1n7Sl17NyQMuwNhchSw6ARd2aGZ9JoDF00CRQ=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ft3s53vfgj", "Integrity": "rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 589087, "LastWriteTime": "2025-07-15T08:12:28.256571+00:00"}, "I5pnxHmx9kY/29ED9AhuMqUhm4PjK5X/HeWjMJj+Njo=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6cfz1n2cew", "Integrity": "mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 207819, "LastWriteTime": "2025-07-15T08:12:28.2585735+00:00"}, "ieKr5+lMmeRwG+/UtVUpOkpBRgFGgnMmdGp8OD+1gBw=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6pdc2jztkx", "Integrity": "Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 444579, "LastWriteTime": "2025-07-15T08:12:28.2595705+00:00"}, "sk2KXAx+aIVg0G7r19to648ZrX74HbdtlC768njfJZY=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "493y06b0oq", "Integrity": "CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 80721, "LastWriteTime": "2025-07-15T08:12:28.2595705+00:00"}, "meOlixs9HUtezcEdKkELpCYBtj1KO2Py1+RJETIH3X0=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "iovd86k7lj", "Integrity": "Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 332090, "LastWriteTime": "2025-07-15T08:12:28.2625705+00:00"}, "sNdCHquIQqZuo+spVqY6LebCAhzmjm2jL+2o63ldoZg=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vr1egmr9el", "Integrity": "exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 135829, "LastWriteTime": "2025-07-15T08:12:28.2651312+00:00"}, "93cblINvw2Na21uGb+1Dwdm6IOOu7IoigYsJ2P4MQjg=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbrnm935zg", "Integrity": "EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 305438, "LastWriteTime": "2025-07-15T08:12:28.2672269+00:00"}, "6znRZ5t1hIvh5Xg7hc3aJI7cb0qyw5qNH+z2qUtnic0=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jj8uyg4cgr", "Integrity": "QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 73935, "LastWriteTime": "2025-07-15T08:12:28.2672269+00:00"}, "+IVGYVP4RozvndxaqM/NG/Ep0gMMTfRayYH9KgwNya4=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "y7v9cxd14o", "Integrity": "Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222455, "LastWriteTime": "2025-07-15T08:12:28.2682251+00:00"}, "iSJIQRFRLYmblGMmXmUz0WJ+UDHfwddknCKwg3Kd42Y=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "notf2xhcfb", "Integrity": "+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 145401, "LastWriteTime": "2025-07-15T08:12:28.270227+00:00"}, "KseCE4v1Ip9gSrQZeEhjgxRSa+z64L0d761LSMgDnpI=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "h1s4sie4z3", "Integrity": "9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 306606, "LastWriteTime": "2025-07-15T08:12:28.2722259+00:00"}, "Xhgn1BBCwLm7iTOsvJDiSbpx9vtz+lvTf2hUZk+tULc=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "63fj8s7r0e", "Integrity": "3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 60635, "LastWriteTime": "2025-07-15T08:12:28.2822242+00:00"}, "h4g6+dT/n+FPSErnH1yIlqeFJG1qoXZW/uATG+r5w9A=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0j3bgjxly4", "Integrity": "ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 220561, "LastWriteTime": "2025-07-15T08:12:28.2832252+00:00"}, "M70qbuzjStmkOP5rKgEbN7y0yxhtXJXRCSk166umZSE=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-07-15T08:12:28.2248917+00:00"}, "dSV+pWx5FErJC9T+Bv1fXbLxqNpw16bWfqi5hU5pjUo=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-07-15T08:12:28.3381948+00:00"}, "qRnXKmecWx96LxU/JCUwqOdiyUJmWuMtRGuTki79+vY=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-07-15T08:12:28.3391958+00:00"}, "DZB8OVfPzjVOn0Ci2xKfdJFL1EpmWTdCn7NUXWQsGSQ=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-07-15T08:12:28.2278933+00:00"}, "5TOdmgqxfDaUmOHYLpciP24yDXKXj8d+VKNSoHrdt/I=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "83jwlth58m", "Integrity": "XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 53033, "LastWriteTime": "2025-07-15T08:12:28.3351944+00:00"}, "PWl97AChSjZLUW5WvWXIMw5r9zhsTBOCuDvlP7hKCsY=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mrlpezrjn3", "Integrity": "jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22125, "LastWriteTime": "2025-07-15T08:12:28.3361947+00:00"}, "eqoRzaO759u6bEKkrtEYGvzxzuEzkADPQtdnLAtV73A=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lzl9nlhx6b", "Integrity": "kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 52536, "LastWriteTime": "2025-07-15T08:12:28.3361947+00:00"}, "r8/tioIf3sKNE8RdKx3ohHJPyvpL4WHeDUK8sjdTBVU=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ag7o75518u", "Integrity": "umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 25308, "LastWriteTime": "2025-07-15T08:12:28.3371945+00:00"}, "PrFMtmZFm2oB659G7rafDELTDAd16furBqRBLspO66w=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-07-15T08:12:28.2268935+00:00"}, "3M/2gY4LNzJpCl1KNpEFWuCvk+hhZEGrJudPPUCncgk=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0i3buxo5is", "Integrity": "eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 285314, "LastWriteTime": "2025-07-15T08:12:28.2862257+00:00"}, "ANeXpHFJAeDIX/CFA+bSESkNzEwTVaMlyDUhqQlqQiI=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o1o13a6vjx", "Integrity": "/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 87533, "LastWriteTime": "2025-07-15T08:12:28.2874744+00:00"}, "A6Fcw7XEHb5d8yVsbJBLfImUUxAmWBaXc7DGyhJz5S4=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ttgo8qnofa", "Integrity": "z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 134755, "LastWriteTime": "2025-07-15T08:12:28.2975013+00:00"}, "6JNFIz7mJ+tQHXGicxcN3Au0zcqV6xEStWCkerPFD+4=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2z0ns9nrw6", "Integrity": "UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 232015, "LastWriteTime": "2025-07-15T08:12:28.3034969+00:00"}, "6aTE0BMMc1KRe5Ma1IOkjsRGhKm36zS7xy/qsa9CvVY=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "muycvpuwrr", "Integrity": "kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 70264, "LastWriteTime": "2025-07-15T08:12:28.3054968+00:00"}, "xFgwIDt/VJXDf77AiVu9on3h35EyHnjpd0neS3Ja6ck=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "87fc7y1x7t", "Integrity": "9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 107143, "LastWriteTime": "2025-07-15T08:12:28.3331961+00:00"}, "fj9OB+7zoZgrEbEh/lScX0OX58wy7NzAQki8MZ5K9XA=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-07-15T08:12:28.2258947+00:00"}, "WGgOtT8u78it3iZpMgnJKqcO4i2azheTf6BtVe6qCNU=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\css\\admin.css", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "css/admin#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "m2s80dm3u7", "Integrity": "4ksfIHr5QWe6rug4IfaM3nQtHxLj5px1wCPlPW2llFA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\admin.css", "FileLength": 5418, "LastWriteTime": "2025-07-15T10:41:05.2888334+00:00"}, "WGS7DAoWfNq9QNbwoVAddj+Q5GMgZw7klkwwkMYL+ww=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\images\\generate-images.html", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "images/generate-images#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "olv5dlnzif", "Integrity": "PABs4GAOZE5bdBlC0efrf1Xj/5M+on77wNhrPaMnCoU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\generate-images.html", "FileLength": 10979, "LastWriteTime": "2025-07-15T09:59:25.5587387+00:00"}, "DJ89P84AfiyysKo8ENmNXlEtJj44mSWJGYKDeQWbqfU=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\images\\hero-bg.svg", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "images/hero-bg#[.{fingerprint}]?.svg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "m1gorpkqn2", "Integrity": "xtnRp1/rELXv4cmcxE3zHqAc4emHzFQB9+yfywEquYQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\hero-bg.svg", "FileLength": 3637, "LastWriteTime": "2025-07-15T10:02:41.6119596+00:00"}, "z3HcRGl40ZcUTQnI4Hpl4oIf0u4ZpvOLaa3VHGIP1lw=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\images\\sinh-to-xoai.svg", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "images/sinh-to-xoai#[.{fingerprint}]?.svg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xkp057e7i2", "Integrity": "fz3UvYJ7+qen9pPadTsqQgAVdLPQwtLuoVkbd+pE8Xg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\sinh-to-xoai.svg", "FileLength": 2979, "LastWriteTime": "2025-07-15T10:01:50.2014603+00:00"}, "VUFpf4CG0NWFwsLls5o1DM9EsVCR3AHse7jefs4KRWE=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\images\\tra-dao.svg", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "images/tra-dao#[.{fingerprint}]?.svg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "1crswvtss8", "Integrity": "n7pt7Ek+eYLiUgYndTjaEKMLBp3ilxYtcDkZgObHXcw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\tra-dao.svg", "FileLength": 3345, "LastWriteTime": "2025-07-15T10:01:06.1710968+00:00"}, "RlYDU4K1XS7FBN91upfyxWrn9BzA7RLlETfDcbhI628=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\images\\tra-sua-truyen-thong.svg", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "images/tra-sua-truyen-thong#[.{fingerprint}]?.svg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "voiw205evl", "Integrity": "nziwP7F/8PpSY43CgvkVZFz2C+WQINfWL5PpXqpO8B0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\tra-sua-truyen-thong.svg", "FileLength": 2444, "LastWriteTime": "2025-07-15T09:59:52.0617583+00:00"}, "o9wLkx6xidQxJ/xS/vOG8j+xox0XK7/CcO/HbtrkBH0=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\js\\site.js", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r1ynguq8uq", "Integrity": "4Qv2tAEpm6j5lNBC4dz/C/5U+MjLayk+3RGiWHZBLk4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 7189, "LastWriteTime": "2025-07-15T09:20:23.5224987+00:00"}, "y4iwwMLQL7UCup/lq+fK3pOms4HMqJnttUm1flhcotc=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\css\\site.css", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bhaizxohn4", "Integrity": "cRNgu3DEgWq1+hXyENgFvaR8N6fjAQ/6/+et7wIkhoU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 12910, "LastWriteTime": "2025-07-15T10:38:00.331431+00:00"}, "oXobXKs0RtCQmhHu0Rut5HryejrFkLYzXfeqcVLKGGI=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\images\\ca-phe-den-da.svg", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "images/ca-phe-den-da#[.{fingerprint}]?.svg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0khjbx7955", "Integrity": "YGH27AOCNkGyaWeJKohdVSlvUt8Y6JfYUeu3HkADli8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\ca-phe-den-da.svg", "FileLength": 3285, "LastWriteTime": "2025-07-15T10:11:26.8585072+00:00"}, "R1ysDv2c+gZqBUpWgNIadBbxWg39bf7V76N7eAUGWkI=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\images\\tra-sua-matcha.svg", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "images/tra-sua-matcha#[.{fingerprint}]?.svg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pyeb9<PERSON><PERSON>", "Integrity": "gZVMTdYqOH0JVL3NMbh0nTyJRBCSmBQHzqWwEM948Zw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\tra-sua-matcha.svg", "FileLength": 2875, "LastWriteTime": "2025-07-15T10:10:54.3914502+00:00"}, "yHsKnOPYHXAqmYhdZJZlnlVWqA9no9a/PoAGt6qlU/o=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\js\\admin.js", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "js/admin#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "g5tq2i8okj", "Integrity": "FGA0klqupSt8I+JGGcZTIwh7pN+Kk+PidwLvfLDgWfY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\admin.js", "FileLength": 1411, "LastWriteTime": "2025-07-15T10:41:31.7957777+00:00"}}, "CachedCopyCandidates": {}}